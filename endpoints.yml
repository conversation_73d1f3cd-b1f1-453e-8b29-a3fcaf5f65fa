# This file contains the different endpoints your bot can use.

# Server where the models are pulled from.
# https://rasa.com/docs/rasa/model-storage#fetching-models-from-a-server

#models:
#  url: http://my-server.com/models/default_core@latest
#  wait_time_between_pulls:  10   # [optional](default: 100)

# Server which runs your custom actions.
# https://rasa.com/docs/rasa/custom-actions

action_endpoint:
  url: "http://localhost:5055/webhook"

# Tracker store which is used to store the conversations.
# By default the conversations are stored in memory.
# https://rasa.com/docs/rasa/tracker-stores

#tracker_store:
#    type: redis
#    url: <host of the redis instance>
#    port: <port of your redis instance>
#    db: <number of your database within redis>
#    password: <password used for authentication>
#    use_ssl: <whether or not the communication is encrypted>

#tracker_store:
#    type: mongod
#    url: <url to your mongo instance>
#    db: <name of the db within your mongo instance>
#    username: <username used for authentication>
#    password: <password used for authentication>

# Event broker which all conversation events should be streamed to.
# https://rasa.com/docs/rasa/event-brokers

#event_broker:
#  type: pika
#  url: localhost
#  username: username
#  password: password
#  queue: queue
