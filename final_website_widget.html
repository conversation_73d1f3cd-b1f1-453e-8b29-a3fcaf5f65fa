<!-- 
🚀 ZIRA SOPHISTICATED BUSINESS INTELLIGENCE WIDGET
Advanced AI Strategic Assistant with Business Intelligence Engine
Copy this code and paste it before the closing </body> tag on your website
-->

<!-- ZIRA Widget Styles -->
<style>
    /* zira Floating Chat Widget */
    #zira-widget {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 9999;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    #zira-chat-button {
        width: 65px;
        height: 65px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        border: none;
        cursor: pointer;
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 26px;
        position: relative;
        overflow: hidden;
    }

    #zira-chat-button:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
    }

    #zira-chat-button.active {
        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    }

    #zira-notification {
        position: absolute;
        top: -5px;
        right: -5px;
        width: 22px;
        height: 22px;
        background: #ff4757;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
        font-weight: bold;
        animation: zira-pulse 2s infinite;
    }

    @keyframes zira-pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.2); }
        100% { transform: scale(1); }
    }

    #zira-chat-window {
        position: absolute;
        bottom: 85px;
        right: 0;
        width: 400px;
        height: 550px;
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
        display: none;
        flex-direction: column;
        overflow: hidden;
        transform: scale(0.8);
        opacity: 0;
        transition: all 0.3s ease;
    }

    #zira-chat-window.show {
        display: flex;
        transform: scale(1);
        opacity: 1;
    }

    .zira-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 18px 22px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .zira-header-info {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .zira-avatar {
        width: 45px;
        height: 45px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
    }

    .zira-status {
        font-size: 13px;
        opacity: 0.9;
    }

    .zira-close {
        background: none;
        border: none;
        color: white;
        font-size: 22px;
        cursor: pointer;
        padding: 6px;
        border-radius: 50%;
        transition: background 0.3s;
    }

    .zira-close:hover {
        background: rgba(255, 255, 255, 0.2);
    }

    .zira-messages {
        flex: 1;
        padding: 22px;
        overflow-y: auto;
        background: #f8f9fa;
    }

    .zira-message {
        margin-bottom: 18px;
        display: flex;
        align-items: flex-start;
    }

    .zira-message.user {
        justify-content: flex-end;
    }

    .zira-message.bot {
        justify-content: flex-start;
    }

    .zira-message-content {
        max-width: 85%;
        padding: 14px 18px;
        border-radius: 18px;
        word-wrap: break-word;
        line-height: 1.5;
        font-size: 14px;
    }

    .zira-message.user .zira-message-content {
        background: #667eea;
        color: white;
        border-bottom-right-radius: 4px;
    }

    .zira-message.bot .zira-message-content {
        background: white;
        color: #333;
        border: 1px solid #e0e0e0;
        border-bottom-left-radius: 4px;
    }

    .zira-quick-actions {
        padding: 12px 22px;
        background: #f8f9fa;
        border-top: 1px solid #e0e0e0;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .zira-quick-action {
        padding: 8px 14px;
        background: white;
        border: 1px solid #667eea;
        color: #667eea;
        border-radius: 16px;
        cursor: pointer;
        font-size: 13px;
        transition: all 0.3s;
        font-weight: 500;
    }

    .zira-quick-action:hover {
        background: #667eea;
        color: white;
    }

    .zira-input-container {
        padding: 18px 22px;
        background: white;
        border-top: 1px solid #e0e0e0;
        display: flex;
        gap: 12px;
        align-items: center;
    }

    .zira-input {
        flex: 1;
        padding: 12px 16px;
        border: 2px solid #e0e0e0;
        border-radius: 22px;
        outline: none;
        font-size: 14px;
        transition: border-color 0.3s;
    }

    .zira-input:focus {
        border-color: #667eea;
    }

    .zira-send {
        width: 44px;
        height: 44px;
        background: #667eea;
        color: white;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.3s;
        font-size: 16px;
    }

    .zira-send:hover {
        background: #5a6fd8;
    }

    .zira-send:disabled {
        background: #ccc;
        cursor: not-allowed;
    }

    .zira-typing {
        display: none;
        padding: 12px 18px;
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 18px;
        border-bottom-left-radius: 4px;
        max-width: 85%;
        margin-bottom: 18px;
    }

    .zira-typing-dots {
        display: flex;
        gap: 5px;
    }

    .zira-typing-dots span {
        width: 8px;
        height: 8px;
        background: #999;
        border-radius: 50%;
        animation: zira-typing 1.4s infinite;
    }

    .zira-typing-dots span:nth-child(2) {
        animation-delay: 0.2s;
    }

    .zira-typing-dots span:nth-child(3) {
        animation-delay: 0.4s;
    }

    @keyframes zira-typing {
        0%, 60%, 100% {
            transform: translateY(0);
        }
        30% {
            transform: translateY(-10px);
        }
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        #zira-chat-window {
            width: 340px;
            height: 500px;
            bottom: 75px;
            right: 10px;
        }
        
        #zira-widget {
            bottom: 15px;
            right: 15px;
        }
    }

    /* Welcome Animation */
    .zira-welcome {
        animation: zira-slideInUp 0.5s ease-out;
    }

    @keyframes zira-slideInUp {
        from {
            transform: translateY(20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
</style>

<!-- ZIRA Widget HTML -->
<div id="zira-widget">
    <!-- Chat Button -->
    <button id="zira-chat-button" onclick="toggleZiraChat()">
        <span id="zira-button-icon">🤖</span>
        <div id="zira-notification" style="display: none;">💡</div>
    </button>

    <!-- Chat Window -->
    <div id="zira-chat-window">
        <!-- Header -->
        <div class="zira-header">
            <div class="zira-header-info">
                <div class="zira-avatar">🧠</div>
                <div>
                    <div style="font-weight: bold; font-size: 16px;">zira</div>
                    <div class="zira-status">AI Strategic Assistant</div>
                </div>
            </div>
            <button class="zira-close" onclick="closeZiraChat()">×</button>
        </div>

        <!-- Messages -->
        <div class="zira-messages" id="zira-messages">
            <div class="zira-message bot zira-welcome">
                <div class="zira-message-content">
                    Hello! I'm zira, your AI strategist from Techpulse Consulting. I specialize in providing precise project assessments with real numbers and strategic insights. 🚀<br><br>What brings you here today? Are you exploring a new project or looking to improve something existing?
                </div>
            </div>
        </div>

        <!-- Typing Indicator -->
        <div class="zira-typing" id="zira-typing">
            <div class="zira-typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="zira-quick-actions">
            <div class="zira-quick-action" onclick="sendQuickMessage('I need a project assessment')">📊 Project Assessment</div>
            <div class="zira-quick-action" onclick="sendQuickMessage('Tell me about partnership models')">🤝 Partnership Models</div>
            <div class="zira-quick-action" onclick="sendQuickMessage('I have an ERP project')">⚙️ ERP Implementation</div>
        </div>

        <!-- Input -->
        <div class="zira-input-container">
            <input type="text" class="zira-input" id="zira-input" placeholder="Describe your project..." onkeypress="handleZiraKeyPress(event)">
            <button class="zira-send" id="zira-send" onclick="sendZiraMessage()">
                <span>➤</span>
            </button>
        </div>
    </div>
</div>

<!-- ZIRA Widget JavaScript -->
<script>
    // ZIRA Sophisticated Business Intelligence Widget
    let ziraIsOpen = false;
    let ziraConversationId = 'user_' + Date.now();
    
    // UPDATE THIS URL FOR YOUR PRODUCTION SERVER
    const ZIRA_API_URL = 'https://your-domain.com/webhooks/rest/webhook';
    // For local testing: 'http://localhost:8000/webhooks/rest/webhook'
    
    function toggleZiraChat() {
        if (ziraIsOpen) {
            closeZiraChat();
        } else {
            openZiraChat();
        }
    }
    
    function openZiraChat() {
        const chatWindow = document.getElementById('zira-chat-window');
        const chatButton = document.getElementById('zira-chat-button');
        const buttonIcon = document.getElementById('zira-button-icon');
        const notification = document.getElementById('zira-notification');
        
        chatWindow.classList.add('show');
        chatButton.classList.add('active');
        buttonIcon.textContent = '×';
        notification.style.display = 'none';
        ziraIsOpen = true;
        
        setTimeout(() => {
            document.getElementById('zira-input').focus();
        }, 300);
        
        // Track engagement
        if (typeof gtag !== 'undefined') {
            gtag('event', 'zira_chat_opened', {
                'event_category': 'engagement',
                'event_label': 'ai_assistant'
            });
        }
    }
    
    function closeZiraChat() {
        const chatWindow = document.getElementById('zira-chat-window');
        const chatButton = document.getElementById('zira-chat-button');
        const buttonIcon = document.getElementById('zira-button-icon');
        
        chatWindow.classList.remove('show');
        chatButton.classList.remove('active');
        buttonIcon.textContent = '🤖';
        ziraIsOpen = false;
    }
    
    function addZiraMessage(content, isUser = false) {
        const messagesContainer = document.getElementById('zira-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `zira-message ${isUser ? 'user' : 'bot'}`;
        
        const messageContent = document.createElement('div');
        messageContent.className = 'zira-message-content';
        messageContent.innerHTML = content.replace(/\n/g, '<br>');
        
        messageDiv.appendChild(messageContent);
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    function showZiraTyping() {
        document.getElementById('zira-typing').style.display = 'block';
        document.getElementById('zira-messages').scrollTop = document.getElementById('zira-messages').scrollHeight;
    }
    
    function hideZiraTyping() {
        document.getElementById('zira-typing').style.display = 'none';
    }
    
    async function sendMessageToZira(message) {
        try {
            const response = await fetch(ZIRA_API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    sender: ziraConversationId,
                    message: message
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('zira Error:', error);
            return [{
                text: "I'm experiencing a technical issue right now. Please try again in a moment, or contact Techpulse Consulting directly for immediate assistance."
            }];
        }
    }
    
    async function sendZiraMessage() {
        const input = document.getElementById('zira-input');
        const sendButton = document.getElementById('zira-send');
        const message = input.value.trim();
        
        if (!message) return;

        addZiraMessage(message, true);
        input.value = '';
        sendButton.disabled = true;
        showZiraTyping();

        const responses = await sendMessageToZira(message);
        hideZiraTyping();

        responses.forEach(response => {
            if (response.text) {
                addZiraMessage(response.text);
            }
        });

        sendButton.disabled = false;
        input.focus();
        
        // Track message sent
        if (typeof gtag !== 'undefined') {
            gtag('event', 'zira_message_sent', {
                'event_category': 'engagement',
                'event_label': 'ai_conversation'
            });
        }
    }
    
    function sendQuickMessage(message) {
        document.getElementById('zira-input').value = message;
        sendZiraMessage();
    }
    
    function handleZiraKeyPress(event) {
        if (event.key === 'Enter') {
            sendZiraMessage();
        }
    }
    
    // Show notification after 8 seconds if chat hasn't been opened
    setTimeout(() => {
        if (!ziraIsOpen) {
            document.getElementById('zira-notification').style.display = 'flex';
        }
    }, 8000);
    
    // Auto-hide notification after 30 seconds
    setTimeout(() => {
        document.getElementById('zira-notification').style.display = 'none';
    }, 30000);
</script>

<!-- 
🎉 INTEGRATION COMPLETE!

Your sophisticated zira AI assistant is now ready with:
✅ Advanced Business Intelligence Engine
✅ Sophisticated Conversation System  
✅ Real-time Project Assessments
✅ Strategic Partnership Recommendations
✅ Human-like Natural Conversations
✅ Professional UI/UX Design

Remember to update ZIRA_API_URL to your production server!
-->
