#!/usr/bin/env python3
"""
Sophisticated Conversation System for zira
Implementing human-like, intelligent conversation flow with advanced business logic
"""

import os
import sys
import json
import logging
import re
from http.server import HTTPServer, BaseHTTPRequestHandler
from advanced_business_intelligence import BusinessI<PERSON>lligenceEngine

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ConversationTracker:
    """Advanced conversation state tracking"""
    
    def __init__(self):
        self.conversation_data = {}
        self.conversation_stage = "greeting"
        self.data_collection_progress = {}
        self.required_fields = [
            'company_name', 'industry', 'company_size', 'project_type',
            'project_scope', 'budget_range', 'timeline_months', 'team_size', 'challenges'
        ]
        self.collected_fields = set()
        self.conversation_history = []
        
    def add_message(self, message, is_user=True):
        """Add message to conversation history"""
        import datetime
        self.conversation_history.append({
            'message': message,
            'is_user': is_user,
            'timestamp': str(datetime.datetime.now())
        })
    
    def extract_information(self, message):
        """Extract business information from user message using NLP techniques"""
        message_lower = message.lower()
        extracted = {}
        
        # Company name extraction
        company_patterns = [
            r'(?:company|business|firm|startup|organization) (?:is |called |named )?([A-Z][A-Za-z\s&]+)',
            r'(?:i work (?:at|for)|we are) ([A-Z][A-Za-z\s&]+)',
            r'([A-Z][A-Za-z\s&]+) (?:is our|is my) (?:company|business)',
            r'(?:at|for) ([A-Z][A-Za-z\s&]+)(?:,|\.|$)'
        ]
        
        for pattern in company_patterns:
            match = re.search(pattern, message)
            if match:
                extracted['company_name'] = match.group(1).strip()
                break
        
        # Industry extraction
        industries = {
            'fintech': ['fintech', 'financial technology', 'payments', 'banking', 'cryptocurrency'],
            'finance': ['finance', 'financial', 'investment', 'trading', 'insurance'],
            'healthcare': ['healthcare', 'medical', 'health', 'pharmaceutical', 'biotech'],
            'technology': ['technology', 'tech', 'software', 'it', 'digital'],
            'manufacturing': ['manufacturing', 'production', 'factory', 'industrial'],
            'retail': ['retail', 'e-commerce', 'shopping', 'consumer', 'sales'],
            'consulting': ['consulting', 'advisory', 'professional services'],
            'education': ['education', 'learning', 'training', 'academic'],
            'nonprofit': ['nonprofit', 'ngo', 'charity', 'foundation']
        }
        
        for industry, keywords in industries.items():
            if any(keyword in message_lower for keyword in keywords):
                extracted['industry'] = industry
                break
        
        # Company size extraction
        size_patterns = [
            (r'(\d+)\s*(?:people|employees|staff|team members)', 'team_size'),
            (r'(?:we are|company of|team of)\s*(\d+)', 'team_size'),
            (r'startup|early stage', 'startup'),
            (r'small (?:company|business)', 'small'),
            (r'medium (?:company|business)', 'medium'),
            (r'large (?:company|business|corporation)', 'large'),
            (r'enterprise|corporation', 'enterprise')
        ]
        
        for pattern, field in size_patterns:
            match = re.search(pattern, message_lower)
            if match:
                if field == 'team_size':
                    extracted['team_size'] = int(match.group(1))
                    # Also determine company size from team size
                    team_size = int(match.group(1))
                    if team_size <= 10:
                        extracted['company_size'] = 'startup'
                    elif team_size <= 50:
                        extracted['company_size'] = 'small'
                    elif team_size <= 250:
                        extracted['company_size'] = 'medium'
                    elif team_size <= 1000:
                        extracted['company_size'] = 'large'
                    else:
                        extracted['company_size'] = 'enterprise'
                else:
                    extracted['company_size'] = field
                break
        
        # Project type extraction
        project_types = {
            'mvp': ['mvp', 'minimum viable product', 'prototype'],
            'software development': ['software', 'application', 'app development', 'system development'],
            'digital transformation': ['digital transformation', 'digitalization', 'modernization'],
            'erp implementation': ['erp implementation', 'implement erp', 'erp system'],
            'erp migration': ['erp migration', 'migrate erp', 'erp upgrade'],
            'mobile app': ['mobile app', 'mobile application', 'ios app', 'android app'],
            'web platform': ['web platform', 'website', 'web application'],
            'data platform': ['data platform', 'analytics', 'business intelligence'],
            'ai integration': ['ai', 'artificial intelligence', 'machine learning', 'automation'],
            'cloud migration': ['cloud migration', 'move to cloud', 'cloud transformation']
        }
        
        for project_type, keywords in project_types.items():
            if any(keyword in message_lower for keyword in keywords):
                extracted['project_type'] = project_type
                break
        
        # Budget extraction
        budget_patterns = [
            r'budget.*?(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:chf|eur|euro|swiss franc)',
            r'(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:chf|eur|euro|swiss franc)',
            r'(\d+)k?\s*(?:chf|eur|euro|swiss franc)',
            r'around\s*(\d+(?:,\d{3})*)',
            r'approximately\s*(\d+(?:,\d{3})*)'
        ]
        
        for pattern in budget_patterns:
            match = re.search(pattern, message_lower)
            if match:
                extracted['budget_range'] = match.group(1)
                break
        
        # Timeline extraction
        timeline_patterns = [
            r'(\d+)\s*months?',
            r'(\d+)\s*weeks?',
            r'by\s*(\w+)\s*(\d{4})',  # by March 2024
            r'in\s*(\d+)\s*months?'
        ]
        
        for pattern in timeline_patterns:
            match = re.search(pattern, message_lower)
            if match:
                if 'week' in pattern:
                    extracted['timeline_months'] = max(1, int(match.group(1)) // 4)
                else:
                    extracted['timeline_months'] = int(match.group(1))
                break
        
        # Challenges extraction
        challenge_keywords = [
            'challenge', 'problem', 'issue', 'difficulty', 'struggle',
            'need', 'require', 'urgent', 'critical', 'important',
            'improve', 'optimize', 'enhance', 'scale', 'grow'
        ]
        
        if any(keyword in message_lower for keyword in challenge_keywords):
            extracted['challenges'] = message
        
        return extracted
    
    def update_data(self, extracted_data):
        """Update conversation data with extracted information"""
        for key, value in extracted_data.items():
            if value is not None:  # Only update if value is not None
                if isinstance(value, str) and value.strip():
                    self.conversation_data[key] = value
                    self.collected_fields.add(key)
                elif isinstance(value, (int, float)):
                    self.conversation_data[key] = value
                    self.collected_fields.add(key)
    
    def get_completion_percentage(self):
        """Get percentage of required data collected"""
        return (len(self.collected_fields) / len(self.required_fields)) * 100
    
    def get_missing_fields(self):
        """Get list of missing required fields"""
        return [field for field in self.required_fields if field not in self.collected_fields]
    
    def is_assessment_ready(self):
        """Check if enough data is collected for assessment"""
        critical_fields = ['company_name', 'industry', 'project_type', 'company_size']
        return all(field in self.collected_fields for field in critical_fields)

class SophisticatedConversationHandler(BaseHTTPRequestHandler):
    """Advanced conversation handler with human-like intelligence"""
    
    conversations = {}
    business_engine = BusinessIntelligenceEngine()
    
    def log_message(self, format, *args):
        logging.info(f"{self.address_string()} - {format % args}")
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
    
    def do_POST(self):
        if self.path == '/webhooks/rest/webhook':
            self.handle_sophisticated_chat()
        elif self.path == '/health':
            self.handle_health_check()
        else:
            self.send_error(404)
    
    def do_GET(self):
        if self.path == '/' or self.path == '/chat':
            self.serve_chat_interface()
        elif self.path == '/health':
            self.handle_health_check()
        else:
            self.send_error(404)
    
    def serve_chat_interface(self):
        try:
            with open('web/index.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404, "Chat interface not found")
    
    def handle_health_check(self):
        health_status = {
            "status": "healthy", 
            "service": "zira Sophisticated Conversation System", 
            "active_conversations": len(self.conversations),
            "business_intelligence": "active"
        }
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(health_status).encode('utf-8'))
    
    def handle_sophisticated_chat(self):
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            sender = data.get('sender', f'user_{len(self.conversations)}')
            message = data.get('message', '').strip()
            
            logging.info(f"Sophisticated chat from {sender}: {message}")
            
            # Initialize or get conversation tracker
            if sender not in self.conversations:
                self.conversations[sender] = ConversationTracker()
            
            tracker = self.conversations[sender]
            tracker.add_message(message, is_user=True)
            
            # Process message with sophisticated intelligence
            responses = self.process_sophisticated_conversation(message, tracker)
            
            # Add responses to conversation history
            for response in responses:
                tracker.add_message(response.get('text', ''), is_user=False)
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(responses, ensure_ascii=False).encode('utf-8'))
            
        except Exception as e:
            logging.error(f"Error in sophisticated chat: {e}")
            error_response = [{
                "text": "I apologize for the technical difficulty. Let me refocus on helping you with your project assessment. Could you tell me about your business challenge?"
            }]
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(error_response).encode('utf-8'))
    
    def process_sophisticated_conversation(self, message, tracker):
        """Process conversation with sophisticated business intelligence"""
        
        # Extract information from current message
        extracted_data = tracker.extract_information(message)
        tracker.update_data(extracted_data)
        
        # Determine conversation stage and generate appropriate response
        if tracker.conversation_stage == "greeting":
            return self.handle_greeting_stage(message, tracker)
        elif tracker.conversation_stage == "data_collection":
            return self.handle_data_collection_stage(message, tracker)
        elif tracker.conversation_stage == "assessment":
            return self.handle_assessment_stage(message, tracker)
        elif tracker.conversation_stage == "results":
            return self.handle_results_stage(message, tracker)
        else:
            return self.handle_general_conversation(message, tracker)
    
    def handle_greeting_stage(self, message, tracker):
        """Handle initial greeting and project identification"""
        
        # Check if user mentioned specific project needs
        if any(keyword in message.lower() for keyword in ['project', 'help', 'assessment', 'estimate', 'cost', 'timeline']):
            tracker.conversation_stage = "data_collection"
            
            # Generate personalized greeting based on extracted information
            company_name = tracker.conversation_data.get('company_name', '')
            industry = tracker.conversation_data.get('industry', '')
            project_type = tracker.conversation_data.get('project_type', '')
            
            greeting = "Hello! I'm zira, and I'm genuinely excited to help you with your project. "
            
            if company_name:
                greeting += f"It's wonderful to meet someone from {company_name}! "
            
            if industry:
                greeting += f"The {industry} sector is fascinating, and I love working with companies in this space. "
            
            if project_type:
                greeting += f"I can already see you're thinking about {project_type} - that's exactly the kind of strategic initiative that can be transformational. "
            
            greeting += "\n\nI specialize in providing precise project assessments with real numbers and strategic insights. To give you the most accurate estimates, I'd love to learn more about your specific situation.\n\n"
            
            # Ask the most relevant next question
            next_question = self.get_next_intelligent_question(tracker)
            
            return [{"text": greeting + next_question}]
        
        else:
            # General greeting
            return [{
                "text": "Hello! I'm zira, your AI strategist from Techpulse Consulting. I'm here to help you explore your project's potential and provide strategic insights.\n\nWhat brings you here today? Are you looking to get estimates for a specific project, or would you like to explore how we could work together?"
            }]
    
    def handle_data_collection_stage(self, message, tracker):
        """Handle intelligent data collection with natural conversation flow"""
        
        # Check if we have enough data for assessment
        if tracker.is_assessment_ready():
            tracker.conversation_stage = "assessment"
            return self.generate_preliminary_assessment(tracker)
        
        # Continue data collection with intelligent questioning
        completion = tracker.get_completion_percentage()
        
        if completion < 50:
            # Focus on critical information first
            response = "Perfect! "
            
            # Acknowledge what we learned
            if tracker.conversation_data.get('company_name'):
                response += f"I love learning about {tracker.conversation_data['company_name']}. "
            
            if tracker.conversation_data.get('project_type'):
                response += f"Your {tracker.conversation_data['project_type']} project sounds really promising. "
            
            response += "\n\n" + self.get_next_intelligent_question(tracker)
            
        else:
            # We have most information, prepare for assessment
            response = "Excellent! I'm getting a clear picture of your project. "
            
            missing_fields = tracker.get_missing_fields()
            if len(missing_fields) <= 2:
                response += "Just one more quick question to give you the most accurate assessment:\n\n"
                response += self.get_next_intelligent_question(tracker)
            else:
                tracker.conversation_stage = "assessment"
                return self.generate_preliminary_assessment(tracker)
        
        return [{"text": response}]
    
    def get_next_intelligent_question(self, tracker):
        """Get the most relevant next question based on conversation context"""
        
        missing_fields = tracker.get_missing_fields()
        collected_data = tracker.conversation_data
        
        # Prioritize questions based on what we already know
        if 'company_name' not in tracker.collected_fields:
            return "What's the name of your company? I'd love to personalize our conversation! 🏢"
        
        elif 'industry' not in tracker.collected_fields:
            return "What industry are you in? This helps me understand your specific market dynamics and challenges."
        
        elif 'project_type' not in tracker.collected_fields:
            return "What type of project are you considering? For example, are you looking at software development, digital transformation, ERP implementation, or something else?"
        
        elif 'company_size' not in tracker.collected_fields and 'team_size' not in tracker.collected_fields:
            return "How large is your team or company? This helps me recommend the right approach and partnership model for your situation."
        
        elif 'project_scope' not in tracker.collected_fields:
            return "Could you tell me more about the scope of your project? What specific outcomes or functionality are you looking to achieve?"
        
        elif 'challenges' not in tracker.collected_fields:
            return "What are the main challenges or goals driving this project? Understanding your 'why' helps me provide much more strategic insights."
        
        elif 'budget_range' not in tracker.collected_fields:
            return "Do you have a budget range in mind? This helps me recommend the most suitable partnership approach for your financial situation."
        
        elif 'timeline_months' not in tracker.collected_fields:
            return "What's your ideal timeline for this project? Are there any specific deadlines or milestones you need to hit?"
        
        else:
            return "I think I have enough information to provide you with a comprehensive assessment. Let me analyze your project!"
    
    def generate_preliminary_assessment(self, tracker):
        """Generate preliminary assessment with business intelligence"""
        
        # Calculate comprehensive assessment
        assessment = self.business_engine.calculate_comprehensive_assessment(tracker.conversation_data)
        
        # Generate human-like response
        company_name = assessment['company_name']
        vii = assessment['value_impact_index']
        project_value = assessment['project_value_chf']
        partnership_model = assessment['partnership_model']
        insights = assessment['strategic_insights']
        
        response = f"🎉 **Exciting Results for {company_name}!**\n\n"
        response += f"I've completed your strategic assessment, and the numbers tell a compelling story about your project's potential!\n\n"
        
        response += f"📊 **Your Strategic Profile:**\n"
        response += f"• Value Impact Index: {vii}/10 (This measures your project's strategic potential)\n"
        response += f"• Estimated Project Value: {project_value:,} CHF\n"
        response += f"• Optimal Timeline: {assessment['project_duration_months']} months\n"
        response += f"• Recommended Partnership: {partnership_model}\n\n"
        
        response += f"💡 **Strategic Insights:**\n"
        for insight in insights[:2]:  # Show top 2 insights
            response += f"• {insight}\n"
        
        response += f"\n🤝 **Why {partnership_model} is Perfect for You:**\n"
        partnership_info = self.business_engine.partnership_models[partnership_model]
        response += f"• {partnership_info['description']}\n"
        response += f"• Payment Structure: {partnership_info['payment_structure']}\n"
        
        response += f"\nWould you like me to explain more about the {partnership_model} model, or do you have questions about any aspect of this assessment?"
        
        tracker.conversation_stage = "results"
        
        return [{"text": response}]
    
    def handle_assessment_stage(self, message, tracker):
        """Handle assessment stage conversations"""
        return self.generate_preliminary_assessment(tracker)
    
    def handle_results_stage(self, message, tracker):
        """Handle post-assessment conversations"""
        
        if any(keyword in message.lower() for keyword in ['partnership', 'payment', 'model']):
            partnership_model = tracker.conversation_data.get('partnership_model', 'Performance')
            partnership_info = self.business_engine.partnership_models.get(partnership_model, {})
            
            response = f"Great question about the {partnership_model} model!\n\n"
            response += f"**How {partnership_model} Works:**\n"
            response += f"• {partnership_info.get('description', '')}\n"
            response += f"• Payment Structure: {partnership_info.get('payment_structure', '')}\n\n"
            
            response += f"This model is specifically designed for companies like yours because it aligns our success with your results. "
            response += f"Would you like to discuss next steps or explore other partnership options?"
            
            return [{"text": response}]
        
        elif any(keyword in message.lower() for keyword in ['next', 'steps', 'contact', 'meeting']):
            response = f"Perfect! I'm excited about the potential for working together.\n\n"
            response += f"**Next Steps:**\n"
            response += f"1. **Detailed Consultation**: Our team can provide a comprehensive project plan\n"
            response += f"2. **Technical Deep Dive**: We'll analyze your specific technical requirements\n"
            response += f"3. **Partnership Agreement**: We'll formalize the collaboration structure\n\n"
            response += f"Would you like me to connect you with our human consultants for a detailed discussion, or do you have more questions about the assessment?"
            
            return [{"text": response}]
        
        else:
            return self.handle_general_conversation(message, tracker)
    
    def handle_general_conversation(self, message, tracker):
        """Handle general conversation with context awareness"""
        
        if any(keyword in message.lower() for keyword in ['thank', 'thanks', 'appreciate']):
            return [{
                "text": f"You're very welcome! It's been a pleasure learning about {tracker.conversation_data.get('company_name', 'your project')}. I'm here whenever you need strategic insights or want to explore new opportunities. Wishing you great success with your initiative!"
            }]
        
        elif any(keyword in message.lower() for keyword in ['bye', 'goodbye', 'exit']):
            return [{
                "text": f"Thank you for the engaging conversation! I hope the assessment provides valuable insights for {tracker.conversation_data.get('company_name', 'your company')}. Feel free to return anytime for strategic guidance. Have a wonderful day!"
            }]
        
        else:
            return [{
                "text": "I want to make sure I'm providing the most helpful information. Could you clarify what specific aspect you'd like to explore further? I'm here to help with project assessments, partnership models, or strategic insights."
            }]

def run_sophisticated_server(port=8000):
    """Run the sophisticated conversation server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, SophisticatedConversationHandler)
    
    logging.info(f"🚀 zira Sophisticated Conversation System starting on port {port}")
    logging.info(f"📱 Chat interface: http://localhost:{port}")
    logging.info(f"🔗 API endpoint: http://localhost:{port}/webhooks/rest/webhook")
    logging.info(f"🧠 Business Intelligence: Active")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        logging.info("🛑 Server stopped")
        httpd.server_close()

if __name__ == "__main__":
    import datetime
    run_sophisticated_server()
