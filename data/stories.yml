version: "3.1"

stories:

- story: happy path assessment
  steps:
  - intent: greet
  - action: utter_greet
  - intent: start_assessment
  - action: utter_start_assessment
  - action: assessment_form
  - active_loop: assessment_form
  - slot_was_set:
    - requested_slot: company_name
  - slot_was_set:
    - company_name: "TechStart"
  - slot_was_set:
    - requested_slot: industry
  - slot_was_set:
    - industry: "technology"
  - slot_was_set:
    - requested_slot: company_size
  - slot_was_set:
    - company_size: "startup"
  - slot_was_set:
    - requested_slot: project_type
  - slot_was_set:
    - project_type: "software development"
  - slot_was_set:
    - requested_slot: project_scope
  - slot_was_set:
    - project_scope: "mobile app"
  - slot_was_set:
    - requested_slot: budget_range
  - slot_was_set:
    - budget_range: "50K-200K CHF"
  - slot_was_set:
    - requested_slot: timeline_months
  - slot_was_set:
    - timeline_months: 6
  - slot_was_set:
    - requested_slot: team_size
  - slot_was_set:
    - team_size: 5
  - slot_was_set:
    - requested_slot: current_challenges
  - slot_was_set:
    - current_challenges: "need digital presence"
  - slot_was_set:
    - requested_slot: null
  - active_loop: null
  - action: action_calculate_assessment
  - action: action_provide_results
  - intent: goodbye
  - action: utter_goodbye

- story: greet and ask about bot
  steps:
  - intent: greet
  - action: utter_greet
  - intent: bot_challenge
  - action: utter_iamabot

- story: simple greeting
  steps:
  - intent: greet
  - action: utter_greet
  - intent: goodbye
  - action: utter_goodbye

- story: assessment with partnership question
  steps:
  - intent: start_assessment
  - action: utter_start_assessment
  - action: assessment_form
  - active_loop: assessment_form
  - slot_was_set:
    - requested_slot: null
  - active_loop: null
  - action: action_calculate_assessment
  - action: action_provide_results
  - intent: ask_partnership_models
  - action: action_explain_partnership_models

- story: direct partnership inquiry
  steps:
  - intent: greet
  - action: utter_greet
  - intent: ask_partnership_models
  - action: action_explain_partnership_models

- story: ask about techpulse
  steps:
  - intent: ask_about_techpulse
  - action: utter_greet

- story: interrupted assessment
  steps:
  - intent: start_assessment
  - action: utter_start_assessment
  - action: assessment_form
  - active_loop: assessment_form
  - intent: ask_about_techpulse
  - action: utter_greet
  - action: assessment_form
  - active_loop: assessment_form

- story: assessment completion and results request
  steps:
  - action: assessment_form
  - active_loop: assessment_form
  - slot_was_set:
    - requested_slot: null
  - active_loop: null
  - intent: request_results
  - action: action_calculate_assessment
  - action: action_provide_results

- story: deny assessment
  steps:
  - intent: greet
  - action: utter_greet
  - intent: start_assessment
  - action: utter_start_assessment
  - intent: deny
  - action: utter_goodbye

- story: affirm and continue
  steps:
  - intent: greet
  - action: utter_greet
  - intent: start_assessment
  - action: utter_start_assessment
  - intent: affirm
  - action: assessment_form
  - active_loop: assessment_form
