version: "3.1"

nlu:
- intent: greet
  examples: |
    - hey
    - hello
    - hi
    - hello there
    - good morning
    - good evening
    - moin
    - hey there
    - let's go
    - hey dude
    - goodmorning
    - goodevening
    - good afternoon
    - hi there
    - howdy
    - what's up
    - hey Z<PERSON><PERSON>
    - hello ZIRA
    - greetings
    - nice to meet you
    - pleasure to meet you
    - I'm here
    - I'd like to chat
    - can we talk
    - I need some help
    - looking for assistance

- intent: goodbye
  examples: |
    - cu
    - good by
    - cee you later
    - good night
    - bye
    - goodbye
    - have a nice day
    - see you around
    - bye bye
    - see you later
    - thank you goodbye
    - thanks bye

- intent: affirm
  examples: |
    - yes
    - y
    - indeed
    - of course
    - that sounds good
    - correct
    - yes please
    - absolutely
    - sure
    - definitely
    - right
    - exactly
    - true
    - ok
    - okay

- intent: deny
  examples: |
    - no
    - n
    - never
    - I don't think so
    - don't like that
    - no way
    - not really
    - not interested
    - nope
    - not at all
    - disagree
    - incorrect
    - wrong

- intent: mood_great
  examples: |
    - perfect
    - great
    - amazing
    - feeling like a king
    - wonderful
    - I am feeling very good
    - I am great
    - I am amazing
    - I am going to save the world
    - super stoked
    - extremely good
    - so so perfect
    - so good
    - so perfect

- intent: mood_unhappy
  examples: |
    - my day was horrible
    - I am sad
    - I don't feel very well
    - I am disappointed
    - super sad
    - I'm so sad
    - sad
    - very sad
    - unhappy
    - not good
    - not very good
    - extremly sad
    - so saad
    - so sad

- intent: bot_challenge
  examples: |
    - are you a bot?
    - are you a human?
    - am I talking to a bot?
    - am I talking to a human?
    - who are you?
    - what are you?
    - are you real?
    - are you artificial?

- intent: start_assessment
  examples: |
    - I want to start an assessment
    - I need a project assessment
    - Can you help me with project planning?
    - I'd like to get a quote
    - I need help with my project
    - Can you assess my project?
    - I want to know about project costs
    - Help me plan my project
    - I need strategic advice
    - Can you analyze my business needs?
    - I want a consultation
    - Let's start the assessment
    - Begin assessment
    - Start evaluation
    - I need project insights
    - We're thinking about a new project
    - I'm exploring options for my business
    - Can you help me understand what this might cost?
    - We need some guidance on our next steps
    - I'm curious about working with Techpulse
    - What would it take to build something like this?
    - I have an idea and need to know if it's viable
    - We're ready to take our business to the next level
    - I'm looking for a strategic partner
    - Can you help me figure out the scope of this project?
    - We need to understand our options
    - I want to explore possibilities
    - Let's see what you can do for us
    - I'm interested in your services
    - We have a challenge that needs solving
    - I need to understand the investment required
    - Can we discuss my project?
    - I'm ready to get started
    - Let's talk business
    - I want to see what's possible

- intent: provide_company_info
  examples: |
    - My company is [TechStart](company_name)
    - We are [Innovate Solutions](company_name)
    - The company name is [Digital Dynamics](company_name)
    - Our company [Global Tech](company_name) operates in [technology](industry)
    - We're a [startup](company_size) called [NextGen](company_name)
    - [MegaCorp](company_name) is a [large corporation](company_size) in [finance](industry)
    - We are [SwissTech AG](company_name), a [medium enterprise](company_size)
    - Our [healthcare](industry) company [MedSolutions](company_name)
    - [RetailPlus](company_name) is in the [retail](industry) sector
    - We're [EcoGreen](company_name), a [small business](company_size) in [sustainability](industry)

- intent: provide_project_info
  examples: |
    - We need [digital transformation](project_type)
    - Looking for [software development](project_type)
    - We want [process optimization](project_type)
    - Need help with [strategic consulting](project_type)
    - The project is about [mobile app development](project_scope)
    - We're planning [cloud migration](project_scope)
    - Our project involves [data analytics platform](project_scope)
    - We need [e-commerce website](project_scope)
    - The scope includes [CRM implementation](project_scope)
    - We want to build [AI chatbot solution](project_scope)

- intent: provide_budget_info
  examples: |
    - Our budget is [under 50K CHF](budget_range)
    - We have [50K-200K CHF](budget_range) available
    - Budget range is [200K-500K CHF](budget_range)
    - We can spend [500K+ CHF](budget_range)
    - Around [100K CHF](budget_range)
    - Budget is approximately [300K CHF](budget_range)
    - We have [limited budget](budget_range)
    - [High budget](budget_range) project
    - [Medium budget](budget_range) available

- intent: provide_timeline_info
  examples: |
    - We need it in [3](timeline_months) months
    - Timeline is [6](timeline_months) months
    - About [12](timeline_months) months
    - [18](timeline_months) months timeline
    - We have [2](timeline_months) months
    - Need it done in [9](timeline_months) months
    - [24](timeline_months) months project
    - Around [4](timeline_months) months
    - [15](timeline_months) months would be ideal

- intent: provide_team_info
  examples: |
    - We have [5](team_size) team members
    - [10](team_size) people will be involved
    - Team size is [3](team_size)
    - About [15](team_size) team members
    - [2](team_size) people on the team
    - [20](team_size) team members available
    - [8](team_size) people in our team
    - Team of [12](team_size)
    - [7](team_size) members

- intent: request_results
  examples: |
    - Show me the results
    - What are the results?
    - Give me the assessment
    - I want to see the analysis
    - Show the calculations
    - What's the outcome?
    - Display results
    - Show assessment results
    - What did you find?
    - Give me the summary

- intent: ask_partnership_models
  examples: |
    - What partnership models do you offer?
    - Tell me about partnership options
    - What are the different models?
    - Explain partnership types
    - What collaboration models exist?
    - How do you work with clients?
    - What are the engagement models?
    - Partnership options available?

- intent: ask_about_techpulse
  examples: |
    - Tell me about Techpulse
    - What is Techpulse Consulting?
    - Who is Techpulse?
    - About your company
    - What does Techpulse do?
    - Techpulse services
    - Company information
    - About Techpulse Consulting
