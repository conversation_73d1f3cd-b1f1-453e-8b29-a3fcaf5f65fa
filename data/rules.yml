version: "3.1"

rules:

- rule: Say goodbye anytime the user says goodbye
  steps:
  - intent: goodbye
  - action: utter_goodbye

- rule: Say 'I am a bot' anytime the user challenges
  steps:
  - intent: bot_challenge
  - action: utter_iamabot

- rule: Activate assessment form
  steps:
  - intent: start_assessment
  - action: utter_start_assessment
  - action: assessment_form
  - active_loop: assessment_form

- rule: Submit assessment form
  condition:
  - active_loop: assessment_form
  steps:
  - action: assessment_form
  - active_loop: null
  - slot_was_set:
    - requested_slot: null
  - action: utter_processing_assessment
  - action: action_calculate_assessment
  - action: action_provide_results

- rule: Handle partnership model questions
  steps:
  - intent: ask_partnership_models
  - action: action_explain_partnership_models

- rule: Handle results request
  condition:
  - slot_was_set:
    - assessment_complete: true
  steps:
  - intent: request_results
  - action: action_provide_results

- rule: Default fallback
  steps:
  - intent: nlu_fallback
  - action: utter_default
