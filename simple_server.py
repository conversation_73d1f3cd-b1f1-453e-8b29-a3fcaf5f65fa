#!/usr/bin/env python3
"""
Simple server for ZIRA chatbot testing
This creates a minimal chat server that simulates Ra<PERSON>'s webhook API
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import sys
import os
import urllib.parse

# Add actions to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'actions'))

from actions.actions import ActionCalculateAssessment, ActionProvideResults, ActionExplainPartnershipModels

class MockTracker:
    """Mock tracker that maintains conversation state"""
    def __init__(self):
        self.slots = {}
        self.conversation_state = "greeting"
        self.form_slots_needed = [
            "company_name", "industry", "company_size", "project_type", 
            "project_scope", "budget_range", "timeline_months", "team_size", "current_challenges"
        ]
        self.current_slot_index = 0
    
    def get_slot(self, slot_name):
        return self.slots.get(slot_name)
    
    def set_slot(self, slot_name, value):
        self.slots[slot_name] = value

class MockDispatcher:
    """Mock dispatcher that collects responses"""
    def __init__(self):
        self.messages = []
    
    def utter_message(self, text=None, **kwargs):
        if text:
            self.messages.append({"text": text})

class ZiraHandler(BaseHTTPRequestHandler):
    """HTTP handler for ZIRA chatbot"""
    
    # Global conversation state (in production, use proper session management)
    conversations = {}
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def do_POST(self):
        """Handle POST requests"""
        if self.path == '/webhooks/rest/webhook':
            self.handle_chat()
        else:
            self.send_error(404)
    
    def do_GET(self):
        """Handle GET requests"""
        if self.path == '/':
            self.serve_chat_interface()
        else:
            self.send_error(404)
    
    def serve_chat_interface(self):
        """Serve the chat interface"""
        try:
            with open('web/index.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Update the Rasa server URL to point to this server
            content = content.replace('http://localhost:5005', 'http://localhost:8000')
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404, "Chat interface not found")
    
    def handle_chat(self):
        """Handle chat messages"""
        try:
            # Read request data
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            sender = data.get('sender', 'user')
            message = data.get('message', '').lower().strip()
            
            # Get or create conversation state
            if sender not in self.conversations:
                self.conversations[sender] = MockTracker()
            
            tracker = self.conversations[sender]
            responses = self.process_message(message, tracker)
            
            # Send response
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(responses).encode('utf-8'))
            
        except Exception as e:
            print(f"Error handling chat: {e}")
            self.send_error(500, str(e))
    
    def process_message(self, message, tracker):
        """Process incoming message and return responses"""
        responses = []
        
        # Handle greetings
        if any(word in message for word in ['hello', 'hi', 'hey', 'greet']):
            if tracker.conversation_state == "greeting":
                responses.append({
                    "text": "Hello! I'm ZIRA, your AI strategist from Techpulse Consulting. I'm here to help you with a preliminary project assessment. How can I assist you today?"
                })
                tracker.conversation_state = "ready"
            else:
                responses.append({"text": "Hello again! How can I help you further?"})
        
        # Handle assessment start
        elif any(word in message for word in ['assessment', 'start', 'project', 'help', 'quote', 'consultation']):
            if tracker.conversation_state in ["greeting", "ready"]:
                responses.append({
                    "text": "Excellent! I'll guide you through a strategic assessment to provide you with preliminary project insights. Let's start with some basic information about your company."
                })
                tracker.conversation_state = "collecting_info"
                tracker.current_slot_index = 0
                responses.extend(self.ask_next_question(tracker))
            else:
                responses.append({"text": "We're already in the assessment process. Please answer the current question."})
        
        # Handle partnership models question
        elif any(word in message for word in ['partnership', 'models', 'collaboration', 'engagement']):
            dispatcher = MockDispatcher()
            action = ActionExplainPartnershipModels()
            action.run(dispatcher, tracker, {})
            responses.extend(dispatcher.messages)
        
        # Handle goodbye
        elif any(word in message for word in ['bye', 'goodbye', 'thanks', 'thank you']):
            responses.append({
                "text": "Thank you for using ZIRA! If you need further assistance, feel free to reach out to Techpulse Consulting. Have a great day!"
            })
            tracker.conversation_state = "ended"
        
        # Handle assessment form filling
        elif tracker.conversation_state == "collecting_info":
            responses.extend(self.handle_form_input(message, tracker))
        
        # Handle results request
        elif any(word in message for word in ['results', 'show', 'calculate', 'assessment']):
            if tracker.get_slot('assessment_complete'):
                dispatcher = MockDispatcher()
                action = ActionProvideResults()
                action.run(dispatcher, tracker, {})
                responses.extend(dispatcher.messages)
            else:
                responses.append({"text": "Please complete the assessment first by starting with 'I want to start an assessment'."})
        
        # Default response
        else:
            if tracker.conversation_state == "collecting_info":
                responses.extend(self.handle_form_input(message, tracker))
            else:
                responses.append({
                    "text": "I'm sorry, I didn't understand that. You can say 'start assessment' to begin a project evaluation, 'partnership models' to learn about our services, or 'hello' to start over."
                })
        
        return responses
    
    def ask_next_question(self, tracker):
        """Ask the next question in the assessment form"""
        questions = [
            "What's the name of your company?",
            "What industry does your company operate in?",
            "How would you describe your company size? (e.g., startup, small business, medium enterprise, large corporation)",
            "What type of project are you considering? (e.g., digital transformation, software development, process optimization, strategic consulting)",
            "Could you describe the scope of your project in a few words?",
            "What's your approximate budget range for this project? (e.g., under 50K CHF, 50K-200K CHF, 200K-500K CHF, 500K+ CHF)",
            "What's your desired timeline for this project in months?",
            "How many team members do you expect to be involved in this project?",
            "What are the main challenges or pain points you're hoping to address with this project?"
        ]
        
        if tracker.current_slot_index < len(questions):
            return [{"text": questions[tracker.current_slot_index]}]
        else:
            return self.complete_assessment(tracker)
    
    def handle_form_input(self, message, tracker):
        """Handle form input and move to next question"""
        slot_names = tracker.form_slots_needed
        
        if tracker.current_slot_index < len(slot_names):
            slot_name = slot_names[tracker.current_slot_index]
            
            # Extract and store the value
            if slot_name in ['timeline_months', 'team_size']:
                # Try to extract numbers
                import re
                numbers = re.findall(r'\d+\.?\d*', message)
                if numbers:
                    tracker.set_slot(slot_name, float(numbers[0]))
                else:
                    return [{"text": f"Please provide a number for {slot_name.replace('_', ' ')}. For example: '6' for months or '5' for team members."}]
            else:
                tracker.set_slot(slot_name, message)
            
            tracker.current_slot_index += 1
            
            if tracker.current_slot_index < len(slot_names):
                return self.ask_next_question(tracker)
            else:
                return self.complete_assessment(tracker)
        
        return [{"text": "Thank you for the information. Let me process your assessment..."}]
    
    def complete_assessment(self, tracker):
        """Complete the assessment and provide results"""
        # Calculate assessment
        dispatcher = MockDispatcher()
        calc_action = ActionCalculateAssessment()
        events = calc_action.run(dispatcher, tracker, {})
        
        # Update tracker with results
        for event in events:
            if hasattr(event, 'get') and event.get('event') == 'slot':
                tracker.set_slot(event.get('name'), event.get('value'))
        
        # Provide results
        results_action = ActionProvideResults()
        results_action.run(dispatcher, tracker, {})
        
        tracker.conversation_state = "completed"
        
        return [{"text": "Thank you for providing all the information! Let me process your assessment and calculate the preliminary results..."}] + dispatcher.messages

def run_server(port=8000):
    """Run the ZIRA test server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, ZiraHandler)
    
    print(f"🚀 ZIRA Chatbot Server starting on port {port}")
    print(f"📱 Chat interface: http://localhost:{port}")
    print(f"🔗 API endpoint: http://localhost:{port}/webhooks/rest/webhook")
    print("Press Ctrl+C to stop the server")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        httpd.server_close()

if __name__ == "__main__":
    run_server()
