<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZIRA Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #5a6fd8;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 ZIRA Connection Test</h1>
        <p>This page tests the connection between the web interface and ZIRA's backend server.</p>
        
        <button class="test-button" onclick="testConnection()">Test Connection</button>
        <button class="test-button" onclick="testGreeting()">Test Greeting</button>
        <button class="test-button" onclick="testAssessment()">Test Assessment Start</button>
        <button class="test-button" onclick="clearResults()">Clear Results</button>
        
        <div id="results"></div>
    </div>

    <script>
        const ZIRA_API_URL = 'http://localhost:8000/webhooks/rest/webhook';
        const resultsDiv = document.getElementById('results');

        function addResult(message, type = 'info') {
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        async function testConnection() {
            addResult('Testing connection to ZIRA server...', 'info');
            
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Connection successful! Server status: ${data.status}`, 'success');
                } else {
                    addResult(`❌ Connection failed! Status: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Connection error: ${error.message}`, 'error');
            }
        }

        async function testGreeting() {
            addResult('Testing greeting message...', 'info');
            
            try {
                const response = await fetch(ZIRA_API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sender: 'test_user_' + Date.now(),
                        message: 'Hello'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data && data.length > 0) {
                        addResult(`✅ Greeting test successful! ZIRA responded: "${data[0].text.substring(0, 100)}..."`, 'success');
                    } else {
                        addResult('❌ No response from ZIRA', 'error');
                    }
                } else {
                    addResult(`❌ Greeting test failed! Status: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Greeting test error: ${error.message}`, 'error');
            }
        }

        async function testAssessment() {
            addResult('Testing assessment start...', 'info');
            
            try {
                const response = await fetch(ZIRA_API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sender: 'test_assessment_' + Date.now(),
                        message: 'I want to start an assessment'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data && data.length > 0) {
                        addResult(`✅ Assessment test successful! ZIRA responded: "${data[0].text.substring(0, 100)}..."`, 'success');
                    } else {
                        addResult('❌ No response from ZIRA for assessment', 'error');
                    }
                } else {
                    addResult(`❌ Assessment test failed! Status: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Assessment test error: ${error.message}`, 'error');
            }
        }

        // Auto-run connection test on page load
        window.onload = function() {
            addResult('🚀 ZIRA Connection Test Started', 'info');
            testConnection();
        };
    </script>
</body>
</html>
