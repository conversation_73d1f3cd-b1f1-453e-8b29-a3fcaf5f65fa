#!/usr/bin/env python3
"""
Production server for ZIRA chatbot
Optimized for deployment on Techpulse Consulting website
"""

import os
import sys
import json
import logging
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import ssl

# Add actions to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'actions'))

try:
    from actions.actions import ActionCalculateAssessment, ActionProvideResults, ActionExplainPartnershipModels
except ImportError as e:
    print(f"Error importing actions: {e}")
    print("Please ensure the actions folder is in the same directory as this script")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('zira.log'),
        logging.StreamHandler()
    ]
)

class MockTracker:
    """Enhanced tracker for production use"""
    def __init__(self):
        self.slots = {}
        self.conversation_state = "greeting"
        self.form_slots_needed = [
            "company_name", "industry", "company_size", "project_type", 
            "project_scope", "budget_range", "timeline_months", "team_size", "current_challenges"
        ]
        self.current_slot_index = 0
        self.session_id = None
    
    def get_slot(self, slot_name):
        return self.slots.get(slot_name)
    
    def set_slot(self, slot_name, value):
        self.slots[slot_name] = value
        logging.info(f"Slot set: {slot_name} = {value}")

class MockDispatcher:
    """Enhanced dispatcher for production use"""
    def __init__(self):
        self.messages = []
    
    def utter_message(self, text=None, **kwargs):
        if text:
            self.messages.append({"text": text})
            logging.info(f"Bot response: {text[:100]}...")

class ZiraProductionHandler(BaseHTTPRequestHandler):
    """Production HTTP handler for ZIRA chatbot"""
    
    # Global conversation state with session management
    conversations = {}
    
    def log_message(self, format, *args):
        """Override to use proper logging"""
        logging.info(f"{self.address_string()} - {format % args}")
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.send_header('Access-Control-Max-Age', '86400')
        self.end_headers()
    
    def do_POST(self):
        """Handle POST requests"""
        try:
            if self.path == '/webhooks/rest/webhook':
                self.handle_chat()
            elif self.path == '/health':
                self.handle_health_check()
            else:
                self.send_error(404, "Endpoint not found")
        except Exception as e:
            logging.error(f"Error in POST handler: {e}")
            self.send_error(500, "Internal server error")
    
    def do_GET(self):
        """Handle GET requests"""
        try:
            if self.path == '/' or self.path == '/chat':
                self.serve_chat_interface()
            elif self.path == '/health':
                self.handle_health_check()
            elif self.path == '/status':
                self.handle_status()
            else:
                self.send_error(404, "Page not found")
        except Exception as e:
            logging.error(f"Error in GET handler: {e}")
            self.send_error(500, "Internal server error")
    
    def serve_chat_interface(self):
        """Serve the chat interface with production settings"""
        try:
            with open('web/index.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Update for production environment
            content = content.replace('http://localhost:8000', '')
            content = content.replace('localhost:8000', window.location.host)
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
            
        except FileNotFoundError:
            logging.error("Chat interface file not found")
            self.send_error(404, "Chat interface not found")
    
    def handle_health_check(self):
        """Health check endpoint for monitoring"""
        health_status = {
            "status": "healthy",
            "service": "ZIRA Chatbot",
            "version": "1.0.0",
            "active_conversations": len(self.conversations)
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(health_status).encode('utf-8'))
    
    def handle_status(self):
        """Status endpoint for administration"""
        status_info = {
            "active_sessions": len(self.conversations),
            "total_conversations": sum(1 for tracker in self.conversations.values() 
                                     if tracker.conversation_state == "completed"),
            "service_uptime": "Running",
            "last_activity": "Active"
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(status_info).encode('utf-8'))
    
    def handle_chat(self):
        """Enhanced chat message handling with error recovery"""
        try:
            # Read and parse request
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length == 0:
                self.send_error(400, "Empty request")
                return
                
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            sender = data.get('sender', f'user_{len(self.conversations)}')
            message = data.get('message', '').strip()
            
            if not message:
                self.send_error(400, "Empty message")
                return
            
            logging.info(f"Received message from {sender}: {message}")
            
            # Get or create conversation state
            if sender not in self.conversations:
                self.conversations[sender] = MockTracker()
                self.conversations[sender].session_id = sender
            
            tracker = self.conversations[sender]
            responses = self.process_message(message.lower(), tracker)
            
            # Send response
            self.send_response(200)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(responses, ensure_ascii=False).encode('utf-8'))
            
        except json.JSONDecodeError:
            logging.error("Invalid JSON in request")
            self.send_error(400, "Invalid JSON")
        except Exception as e:
            logging.error(f"Error handling chat: {e}")
            error_response = [{
                "text": "I apologize, but I'm experiencing a technical issue. Please try again or contact Techpulse Consulting directly."
            }]
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(error_response).encode('utf-8'))
    
    def process_message(self, message, tracker):
        """Enhanced message processing with better conversation flow"""
        responses = []
        
        try:
            # Handle greetings
            if any(word in message for word in ['hello', 'hi', 'hey', 'greet', 'good morning', 'good afternoon']):
                if tracker.conversation_state == "greeting":
                    responses.append({
                        "text": "Hello! I'm ZIRA, your AI strategist from Techpulse Consulting. 🚀\n\nI specialize in providing preliminary project assessments and strategic recommendations. I can help you:\n\n• Evaluate your project's potential value\n• Estimate realistic timelines\n• Recommend the best partnership model\n\nHow can I assist you today?"
                    })
                    tracker.conversation_state = "ready"
                else:
                    responses.append({"text": "Hello again! How can I help you further with your project assessment?"})
            
            # Handle assessment start
            elif any(word in message for word in ['assessment', 'start', 'project', 'help', 'quote', 'consultation', 'evaluate']):
                if tracker.conversation_state in ["greeting", "ready"]:
                    responses.append({
                        "text": "Excellent! I'll guide you through a comprehensive strategic assessment. This will take about 3-5 minutes and will provide you with:\n\n📊 **Project Value Estimation** (in CHF)\n⏱️ **Realistic Timeline** (in months)\n🤝 **Partnership Model Recommendation**\n\nLet's begin with some basic information about your company."
                    })
                    tracker.conversation_state = "collecting_info"
                    tracker.current_slot_index = 0
                    responses.extend(self.ask_next_question(tracker))
                else:
                    responses.append({"text": "We're already working on your assessment. Please answer the current question, or say 'restart' to begin again."})
            
            # Handle restart
            elif 'restart' in message:
                tracker.conversation_state = "ready"
                tracker.current_slot_index = 0
                tracker.slots = {}
                responses.append({"text": "Assessment restarted! Let's begin fresh. Say 'start assessment' when you're ready."})
            
            # Handle partnership models question
            elif any(word in message for word in ['partnership', 'models', 'collaboration', 'engagement', 'pricing']):
                dispatcher = MockDispatcher()
                action = ActionExplainPartnershipModels()
                action.run(dispatcher, tracker, {})
                responses.extend(dispatcher.messages)
            
            # Handle goodbye
            elif any(word in message for word in ['bye', 'goodbye', 'thanks', 'thank you', 'exit']):
                responses.append({
                    "text": "Thank you for using ZIRA! 🙏\n\nIf you need further assistance, please don't hesitate to contact Techpulse Consulting directly.\n\n📧 Contact us for detailed consultations\n🌐 Visit: techpulseconsulting.odoo.com\n\nHave a wonderful day!"
                })
                tracker.conversation_state = "ended"
            
            # Handle assessment form filling
            elif tracker.conversation_state == "collecting_info":
                responses.extend(self.handle_form_input(message, tracker))
            
            # Handle results request
            elif any(word in message for word in ['results', 'show', 'calculate', 'summary']):
                if tracker.get_slot('assessment_complete'):
                    dispatcher = MockDispatcher()
                    action = ActionProvideResults()
                    action.run(dispatcher, tracker, {})
                    responses.extend(dispatcher.messages)
                else:
                    responses.append({"text": "I don't have a completed assessment yet. Please start with 'I want to start an assessment' to begin the evaluation process."})
            
            # Default response with helpful suggestions
            else:
                if tracker.conversation_state == "collecting_info":
                    responses.extend(self.handle_form_input(message, tracker))
                else:
                    responses.append({
                        "text": "I'd be happy to help! Here's what I can do:\n\n🎯 **'Start assessment'** - Begin project evaluation\n🤝 **'Partnership models'** - Learn about our collaboration options\n📞 **'Contact'** - Get in touch with Techpulse\n👋 **'Hello'** - Start over\n\nWhat would you like to explore?"
                    })
            
        except Exception as e:
            logging.error(f"Error processing message: {e}")
            responses.append({
                "text": "I apologize for the technical difficulty. Please try rephrasing your message or contact Techpulse Consulting directly for assistance."
            })
        
        return responses
    
    def ask_next_question(self, tracker):
        """Ask the next question with enhanced guidance"""
        questions = [
            "What's the name of your company? 🏢",
            "What industry does your company operate in? (e.g., technology, finance, healthcare, retail) 🏭",
            "How would you describe your company size?\n• Startup (1-10 employees)\n• Small business (11-50 employees)\n• Medium enterprise (51-250 employees)\n• Large corporation (250+ employees) 📈",
            "What type of project are you considering?\n• Digital transformation\n• Software development\n• Process optimization\n• Strategic consulting\n• Other (please specify) 💻",
            "Could you describe the scope of your project in a few words? (e.g., mobile app, website, CRM system, data platform) 🎯",
            "What's your approximate budget range for this project?\n• Under 50K CHF\n• 50K-200K CHF\n• 200K-500K CHF\n• 500K+ CHF 💰",
            "What's your desired timeline for this project in months? (e.g., 3, 6, 12) ⏰",
            "How many team members do you expect to be involved in this project? 👥",
            "What are the main challenges or pain points you're hoping to address? (This helps me recommend the best approach) 🎯"
        ]
        
        if tracker.current_slot_index < len(questions):
            return [{"text": questions[tracker.current_slot_index]}]
        else:
            return self.complete_assessment(tracker)
    
    def handle_form_input(self, message, tracker):
        """Enhanced form input handling with validation"""
        slot_names = tracker.form_slots_needed
        
        if tracker.current_slot_index < len(slot_names):
            slot_name = slot_names[tracker.current_slot_index]
            
            # Extract and validate the value
            if slot_name in ['timeline_months', 'team_size']:
                import re
                numbers = re.findall(r'\d+\.?\d*', message)
                if numbers:
                    value = float(numbers[0])
                    if slot_name == 'timeline_months' and (value < 1 or value > 60):
                        return [{"text": "Please provide a realistic timeline between 1 and 60 months. ⏰"}]
                    elif slot_name == 'team_size' and (value < 1 or value > 100):
                        return [{"text": "Please provide a team size between 1 and 100 members. 👥"}]
                    tracker.set_slot(slot_name, value)
                else:
                    return [{"text": f"Please provide a number for {slot_name.replace('_', ' ')}. For example: '6' for months or '5' for team members. 🔢"}]
            else:
                tracker.set_slot(slot_name, message)
            
            tracker.current_slot_index += 1
            
            if tracker.current_slot_index < len(slot_names):
                return self.ask_next_question(tracker)
            else:
                return self.complete_assessment(tracker)
        
        return [{"text": "Thank you for the information. Processing your assessment... ⚡"}]
    
    def complete_assessment(self, tracker):
        """Complete assessment with enhanced presentation"""
        try:
            # Calculate assessment
            dispatcher = MockDispatcher()
            calc_action = ActionCalculateAssessment()
            events = calc_action.run(dispatcher, tracker, {})
            
            # Update tracker with results
            for event in events:
                if hasattr(event, 'get') and event.get('event') == 'slot':
                    tracker.set_slot(event.get('name'), event.get('value'))
            
            # Provide results
            results_action = ActionProvideResults()
            results_action.run(dispatcher, tracker, {})
            
            tracker.conversation_state = "completed"
            
            return [
                {"text": "🔄 Processing your comprehensive assessment..."},
                {"text": "✅ Analysis complete! Here are your results:"}
            ] + dispatcher.messages + [
                {"text": "Would you like me to explain any of these recommendations in more detail, or shall I connect you with our team for the next steps? 🚀"}
            ]
            
        except Exception as e:
            logging.error(f"Error completing assessment: {e}")
            return [{"text": "I encountered an issue completing your assessment. Please contact Techpulse Consulting directly for a personalized consultation."}]

def run_production_server(port=8000, use_ssl=False, cert_file=None, key_file=None):
    """Run the production ZIRA server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, ZiraProductionHandler)
    
    if use_ssl and cert_file and key_file:
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        context.load_cert_chain(cert_file, key_file)
        httpd.socket = context.wrap_socket(httpd.socket, server_side=True)
        protocol = "HTTPS"
    else:
        protocol = "HTTP"
    
    logging.info(f"🚀 ZIRA Production Server starting on {protocol} port {port}")
    logging.info(f"📱 Chat interface: {protocol.lower()}://your-domain.com:{port}")
    logging.info(f"🔗 API endpoint: {protocol.lower()}://your-domain.com:{port}/webhooks/rest/webhook")
    logging.info(f"💚 Health check: {protocol.lower()}://your-domain.com:{port}/health")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        logging.info("🛑 Server stopped by user")
        httpd.server_close()
    except Exception as e:
        logging.error(f"Server error: {e}")
        httpd.server_close()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='ZIRA Production Server')
    parser.add_argument('--port', type=int, default=8000, help='Port to run on')
    parser.add_argument('--ssl', action='store_true', help='Enable SSL')
    parser.add_argument('--cert', type=str, help='SSL certificate file')
    parser.add_argument('--key', type=str, help='SSL key file')
    
    args = parser.parse_args()
    
    run_production_server(
        port=args.port,
        use_ssl=args.ssl,
        cert_file=args.cert,
        key_file=args.key
    )
