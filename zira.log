2025-06-19 02:19:47,048 - INFO - 🚀 ZIRA Production Server starting on HTTP port 8000
2025-06-19 02:19:47,048 - INFO - 📱 Chat interface: http://your-domain.com:8000
2025-06-19 02:19:47,048 - INFO - 🔗 API endpoint: http://your-domain.com:8000/webhooks/rest/webhook
2025-06-19 02:19:47,048 - INFO - 💚 Health check: http://your-domain.com:8000/health
2025-06-19 02:20:14,746 - INFO - Received message from test_user: Hello! I am interested in exploring possibilities for my business
2025-06-19 02:20:14,746 - INFO - 1******** - "POST /webhooks/rest/webhook HTTP/1.1" 200 -
2025-06-19 02:20:20,776 - ERROR - Error in GET handler: name 'window' is not defined
2025-06-19 02:20:20,777 - INFO - 1******** - code 500, message Internal server error
2025-06-19 02:20:20,778 - INFO - 1******** - "GET / HTTP/1.1" 500 -
2025-06-19 02:20:41,927 - ERROR - Error in GET handler: name 'window' is not defined
2025-06-19 02:20:41,930 - INFO - 1******** - code 500, message Internal server error
2025-06-19 02:20:41,930 - INFO - 1******** - "GET / HTTP/1.1" 500 -
2025-06-19 02:22:29,683 - INFO - 🚀 ZIRA Production Server starting on HTTP port 8000
2025-06-19 02:22:29,683 - INFO - 📱 Chat interface: http://your-domain.com:8000
2025-06-19 02:22:29,683 - INFO - 🔗 API endpoint: http://your-domain.com:8000/webhooks/rest/webhook
2025-06-19 02:22:29,683 - INFO - 💚 Health check: http://your-domain.com:8000/health
2025-06-19 02:22:48,215 - INFO - 1******** - "GET / HTTP/1.1" 200 -
2025-06-19 02:25:39,944 - INFO - 1******** - "GET / HTTP/1.1" 200 -
2025-06-19 02:28:28,827 - INFO - 1******** - "GET / HTTP/1.1" 200 -
2025-06-19 02:30:54,032 - INFO - Received message from test_user: Hello, I would like to know if you can help me with a project
2025-06-19 02:30:54,035 - INFO - 1******** - "POST /webhooks/rest/webhook HTTP/1.1" 200 -
2025-06-19 02:31:21,898 - INFO - 1******** - "GET / HTTP/1.1" 200 -
2025-06-19 02:31:41,687 - INFO - Received message from user: Hello, I would like to know if you can help me with a project that I have for a digital transformation consultancy in Zurich, Switzerland.
2025-06-19 02:31:41,689 - INFO - 1******** - "POST /webhooks/rest/webhook HTTP/1.1" 200 -
2025-06-19 02:32:15,556 - INFO - Received message from user: I have a FinTech startup for the Zurich market. What is the approximate timeline and cost for an MVP, and which partnership model works best for a seed company
2025-06-19 02:32:15,557 - INFO - 1******** - "POST /webhooks/rest/webhook HTTP/1.1" 200 -
2025-06-19 02:32:22,140 - INFO - Received message from test_conversation: I want to start an assessment
2025-06-19 02:32:22,140 - INFO - 1******** - "POST /webhooks/rest/webhook HTTP/1.1" 200 -
2025-06-19 02:32:45,351 - INFO - Received message from user: I have a FinTech startup for the Zurich market. What is the approximate timeline and cost for an MVP, and which partnership model works best for a seed company
2025-06-19 02:32:45,351 - INFO - 1******** - "POST /webhooks/rest/webhook HTTP/1.1" 200 -
2025-06-19 02:33:03,713 - INFO - 1******** - code 404, message Page not found
2025-06-19 02:33:03,723 - INFO - 1******** - "GET /test_web_interface.html HTTP/1.1" 404 -
2025-06-19 02:33:09,257 - INFO - 1******** - "GET / HTTP/1.1" 200 -
2025-06-19 02:33:10,284 - INFO - 1******** - "GET / HTTP/1.1" 200 -
2025-06-19 02:33:24,806 - INFO - 1******** - "GET /health HTTP/1.1" 200 -
2025-06-19 02:33:34,473 - INFO - 1******** - "GET /health HTTP/1.1" 200 -
2025-06-19 02:33:35,442 - INFO - 1******** - "OPTIONS /webhooks/rest/webhook HTTP/1.1" 200 -
2025-06-19 02:33:35,444 - INFO - Received message from test_user_1750293215438: Hello
2025-06-19 02:33:35,444 - INFO - 1******** - "POST /webhooks/rest/webhook HTTP/1.1" 200 -
2025-06-19 02:33:36,022 - INFO - Received message from test_assessment_1750293216017: I want to start an assessment
2025-06-19 02:33:36,023 - INFO - 1******** - "POST /webhooks/rest/webhook HTTP/1.1" 200 -
2025-06-19 02:33:37,873 - INFO - 1******** - "GET /health HTTP/1.1" 200 -
2025-06-19 02:33:38,699 - INFO - Received message from test_user_1750293218695: Hello
2025-06-19 02:33:38,699 - INFO - 1******** - "POST /webhooks/rest/webhook HTTP/1.1" 200 -
2025-06-19 02:33:39,048 - INFO - Received message from test_assessment_1750293219046: I want to start an assessment
2025-06-19 02:33:39,048 - INFO - 1******** - "POST /webhooks/rest/webhook HTTP/1.1" 200 -
2025-06-19 02:33:47,893 - INFO - 1******** - "GET / HTTP/1.1" 200 -
2025-06-19 02:34:05,545 - INFO - Received message from user: I have a FinTech startup for the Zurich market. What is the approximate timeline and cost for an MVP, and which partnership model works best for a seed company
2025-06-19 02:34:05,545 - INFO - 1******** - "POST /webhooks/rest/webhook HTTP/1.1" 200 -
2025-06-19 02:34:15,540 - INFO - Received message from user: My manufacturing SME near Zurich is struggling with an old ERP system. Can you estimate the value of a cloud ERP migration and recommend a payment plan that works with our cash flow?
2025-06-19 02:34:15,540 - INFO - 1******** - "POST /webhooks/rest/webhook HTTP/1.1" 200 -
2025-06-19 02:35:00,814 - INFO - Received message from user: I don't understand you. Am I talking to a bot? I thought I was talking to a person who was going to help me calculate the time my project can possibly have with you, the costs, and the method of association. But I see it's a chatbot just like the others.
2025-06-19 02:35:00,815 - INFO - 1******** - "POST /webhooks/rest/webhook HTTP/1.1" 200 -
2025-06-19 02:36:49,466 - INFO - 1******** - "GET /health HTTP/1.1" 200 -
2025-06-19 02:36:51,307 - INFO - 1******** - "GET /health HTTP/1.1" 200 -
2025-06-19 02:36:51,717 - INFO - Received message from test_user_1750293411712: Hello
2025-06-19 02:36:51,717 - INFO - 1******** - "POST /webhooks/rest/webhook HTTP/1.1" 200 -
2025-06-19 02:36:52,158 - INFO - Received message from test_assessment_1750293412154: I want to start an assessment
2025-06-19 02:36:52,158 - INFO - 1******** - "POST /webhooks/rest/webhook HTTP/1.1" 200 -
