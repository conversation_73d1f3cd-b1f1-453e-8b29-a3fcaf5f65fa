from typing import Any, Text, Dict, List
from rasa_sdk import Action, Tracker, FormValidationAction
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.events import SlotSet
import re
import math


class ActionCalculateAssessment(Action):
    """Calculate project assessment based on collected information"""

    def name(self) -> Text:
        return "action_calculate_assessment"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        # Get slot values
        company_name = tracker.get_slot("company_name")
        industry = tracker.get_slot("industry")
        company_size = tracker.get_slot("company_size")
        project_type = tracker.get_slot("project_type")
        project_scope = tracker.get_slot("project_scope")
        budget_range = tracker.get_slot("budget_range")
        timeline_months = tracker.get_slot("timeline_months")
        team_size = tracker.get_slot("team_size")
        current_challenges = tracker.get_slot("current_challenges")

        # Calculate Value Impact Index (VII)
        vii_score = self.calculate_vii(
            industry, company_size, project_type, 
            project_scope, current_challenges
        )

        # Calculate project value
        project_value = self.calculate_project_value(
            budget_range, vii_score, timeline_months, team_size
        )

        # Calculate timeline
        calculated_timeline = self.calculate_timeline(
            project_type, project_scope, team_size, timeline_months
        )

        # Determine partnership model
        partnership_model = self.determine_partnership_model(
            company_size, budget_range, project_type, current_challenges
        )

        # Store results in slots
        return [
            SlotSet("assessment_complete", True),
            SlotSet("calculated_value", project_value),
            SlotSet("calculated_timeline", calculated_timeline),
            SlotSet("recommended_partnership", partnership_model)
        ]

    def calculate_vii(self, industry, company_size, project_type, project_scope, challenges):
        """Calculate Value Impact Index based on multiple factors"""
        base_score = 1.0

        # Industry multipliers
        industry_multipliers = {
            "technology": 1.3,
            "finance": 1.2,
            "healthcare": 1.4,
            "retail": 1.1,
            "manufacturing": 1.2,
            "consulting": 1.1,
            "education": 1.0,
            "government": 1.1
        }

        # Company size multipliers
        size_multipliers = {
            "startup": 1.4,
            "small business": 1.2,
            "medium enterprise": 1.1,
            "large corporation": 1.0
        }

        # Project type multipliers
        project_multipliers = {
            "digital transformation": 1.5,
            "software development": 1.3,
            "process optimization": 1.2,
            "strategic consulting": 1.4,
            "data analytics": 1.3,
            "cloud migration": 1.2,
            "ai implementation": 1.6
        }

        # Apply multipliers
        if industry and industry.lower() in industry_multipliers:
            base_score *= industry_multipliers[industry.lower()]

        if company_size and company_size.lower() in size_multipliers:
            base_score *= size_multipliers[company_size.lower()]

        if project_type and project_type.lower() in project_multipliers:
            base_score *= project_multipliers[project_type.lower()]

        # Challenge complexity multiplier
        if challenges:
            challenge_keywords = ["urgent", "critical", "complex", "strategic", "transformation"]
            for keyword in challenge_keywords:
                if keyword in challenges.lower():
                    base_score *= 1.1

        return round(base_score, 2)

    def calculate_project_value(self, budget_range, vii_score, timeline_months, team_size):
        """Calculate project value in CHF"""
        # Base value from budget range
        budget_values = {
            "under 50k chf": 35000,
            "50k-200k chf": 125000,
            "200k-500k chf": 350000,
            "500k+ chf": 750000
        }

        base_value = 100000  # Default
        if budget_range:
            for key, value in budget_values.items():
                if key in budget_range.lower():
                    base_value = value
                    break

        # Apply VII multiplier
        adjusted_value = base_value * vii_score

        # Timeline adjustment
        if timeline_months:
            if timeline_months < 6:
                adjusted_value *= 1.2  # Rush premium
            elif timeline_months > 18:
                adjusted_value *= 0.9  # Long-term discount

        # Team size adjustment
        if team_size:
            if team_size > 10:
                adjusted_value *= 1.1  # Large team complexity
            elif team_size < 3:
                adjusted_value *= 0.95  # Small team efficiency

        return round(adjusted_value)

    def calculate_timeline(self, project_type, project_scope, team_size, desired_timeline):
        """Calculate realistic timeline in months"""
        # Base timeline by project type
        base_timelines = {
            "software development": 6,
            "digital transformation": 12,
            "process optimization": 4,
            "strategic consulting": 3,
            "data analytics": 8,
            "cloud migration": 5,
            "ai implementation": 10
        }

        base_timeline = 6  # Default
        if project_type:
            for key, value in base_timelines.items():
                if key in project_type.lower():
                    base_timeline = value
                    break

        # Scope complexity adjustment
        if project_scope:
            complex_keywords = ["platform", "enterprise", "integration", "migration", "transformation"]
            for keyword in complex_keywords:
                if keyword in project_scope.lower():
                    base_timeline *= 1.3
                    break

        # Team size adjustment
        if team_size:
            if team_size > 8:
                base_timeline *= 0.8  # More resources
            elif team_size < 4:
                base_timeline *= 1.2  # Limited resources

        # Compare with desired timeline
        if desired_timeline:
            realistic_timeline = max(base_timeline, desired_timeline * 0.8)
        else:
            realistic_timeline = base_timeline

        return round(realistic_timeline, 1)

    def determine_partnership_model(self, company_size, budget_range, project_type, challenges):
        """Determine the best partnership model"""
        
        # Crisis-to-Success Model
        if challenges and any(word in challenges.lower() for word in ["crisis", "urgent", "critical", "emergency"]):
            return "Crisis-to-Success"
        
        # Performance Model
        if company_size and "startup" in company_size.lower():
            return "Performance"
        
        # Equity Model
        if budget_range and "under 50k" in budget_range.lower() and company_size and "startup" in company_size.lower():
            return "Equity"
        
        # Premium Model
        if budget_range and "500k+" in budget_range.lower():
            return "Premium"
        
        # Monthly Flow Model (default)
        return "Monthly Flow"


class ActionProvideResults(Action):
    """Provide assessment results to the user"""

    def name(self) -> Text:
        return "action_provide_results"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        company_name = tracker.get_slot("company_name")
        calculated_value = tracker.get_slot("calculated_value")
        calculated_timeline = tracker.get_slot("calculated_timeline")
        recommended_partnership = tracker.get_slot("recommended_partnership")

        # Format results message
        results_message = f"""
🎯 **STRATEGIC ASSESSMENT RESULTS for {company_name or 'Your Company'}**

📊 **Project Value**: {calculated_value:,} CHF
⏱️ **Estimated Timeline**: {calculated_timeline} months
🤝 **Recommended Partnership**: {recommended_partnership} Model

✨ **Next Steps**:
1. Schedule a detailed consultation with our experts
2. Receive a comprehensive project proposal
3. Begin your transformation journey with Techpulse

Would you like me to explain the recommended partnership model or connect you with our team?
        """

        dispatcher.utter_message(text=results_message.strip())
        
        return []


class ActionExplainPartnershipModels(Action):
    """Explain the 5 partnership models"""

    def name(self) -> Text:
        return "action_explain_partnership_models"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        partnership_info = """
🤝 **TECHPULSE PARTNERSHIP MODELS**

**1. Crisis-to-Success Model**
- For urgent, critical situations
- Rapid response and implementation
- Success-based pricing with premium rates

**2. Performance Model**
- Results-driven approach
- Payment tied to achieved KPIs
- Ideal for growth-focused companies

**3. Equity Model**
- Partnership through equity exchange
- Long-term collaboration
- Perfect for startups with limited cash flow

**4. Premium Model**
- Full-service, white-glove experience
- Dedicated team and priority support
- For high-value, complex projects

**5. Monthly Flow Model**
- Predictable monthly payments
- Flexible scope adjustments
- Suitable for ongoing partnerships

Each model is designed to align our success with yours. Would you like details about a specific model?
        """

        dispatcher.utter_message(text=partnership_info.strip())
        
        return []


class ValidateAssessmentForm(FormValidationAction):
    """Validate assessment form inputs"""

    def name(self) -> Text:
        return "validate_assessment_form"

    def validate_timeline_months(
        self,
        slot_value: Any,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: Dict[Text, Any],
    ) -> Dict[Text, Any]:
        """Validate timeline input"""
        
        if slot_value is None:
            return {"timeline_months": None}
        
        try:
            timeline = float(slot_value)
            if 1 <= timeline <= 60:  # 1 month to 5 years
                return {"timeline_months": timeline}
            else:
                dispatcher.utter_message(text="Please provide a timeline between 1 and 60 months.")
                return {"timeline_months": None}
        except (ValueError, TypeError):
            dispatcher.utter_message(text="Please provide the timeline as a number of months.")
            return {"timeline_months": None}

    def validate_team_size(
        self,
        slot_value: Any,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: Dict[Text, Any],
    ) -> Dict[Text, Any]:
        """Validate team size input"""
        
        if slot_value is None:
            return {"team_size": None}
        
        try:
            team_size = float(slot_value)
            if 1 <= team_size <= 100:  # 1 to 100 team members
                return {"team_size": team_size}
            else:
                dispatcher.utter_message(text="Please provide a team size between 1 and 100 members.")
                return {"team_size": None}
        except (ValueError, TypeError):
            dispatcher.utter_message(text="Please provide the team size as a number.")
            return {"team_size": None}
