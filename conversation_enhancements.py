#!/usr/bin/env python3
"""
Conversation Enhancement Module for ZIRA
Adds organic, personalized conversation capabilities
"""

import random
from typing import Dict, List, Any

class ConversationEnhancer:
    """Enhances ZIRA's conversation with organic, personalized responses"""
    
    def __init__(self):
        self.conversation_starters = [
            "I'm genuinely excited to learn about your project! 🌟",
            "This is going to be interesting - I love discovering new business opportunities! 💡",
            "Perfect timing! I'm here to help you explore amazing possibilities. 🚀",
            "Wonderful! Let's dive into what could be an incredible journey together. ✨"
        ]
        
        self.encouragement_phrases = [
            "That's fantastic!",
            "Excellent choice!",
            "I love that direction!",
            "Perfect!",
            "Wonderful!",
            "That's brilliant!",
            "Great thinking!",
            "Smart approach!"
        ]
        
        self.transition_phrases = [
            "Now I'm curious about...",
            "This is getting exciting! Tell me about...",
            "Building on that, I'd love to know...",
            "Perfect! Next, let's explore...",
            "Excellent! Now for the interesting part...",
            "Great! This leads me to wonder about..."
        ]
    
    def get_personalized_greeting(self, time_of_day=None):
        """Generate personalized greeting based on time"""
        base_greetings = [
            "Hello there! 👋 I'm ZIRA, your dedicated AI strategist from Techpulse Consulting.",
            "Hi! 🌟 Welcome to Techpulse Consulting. I'm ZIRA, and I'm here to help you succeed.",
            "Hello! 😊 I'm ZIRA from Techpulse Consulting - your friendly business strategist."
        ]
        
        time_specific = {
            "morning": [
                "Good morning! ☀️ I'm ZIRA, and I'm energized to help you start your day with some strategic insights!",
                "Morning! 🌅 I'm ZIRA from Techpulse Consulting, ready to help you make today productive!"
            ],
            "afternoon": [
                "Good afternoon! 🌞 I'm ZIRA, your AI strategist, here to brighten your day with valuable insights!",
                "Afternoon! ⭐ I'm ZIRA from Techpulse Consulting, ready to help you make progress!"
            ],
            "evening": [
                "Good evening! 🌙 I'm ZIRA, working late to help ambitious businesses like yours succeed!",
                "Evening! ✨ I'm ZIRA from Techpulse Consulting, here to help you plan for tomorrow's success!"
            ]
        }
        
        if time_of_day and time_of_day in time_specific:
            return random.choice(time_specific[time_of_day])
        else:
            return random.choice(base_greetings)
    
    def get_encouraging_response(self, context="general"):
        """Get encouraging response based on context"""
        responses = {
            "company_name": [
                "What a great company name! 🏢",
                "I love that name! 🌟",
                "That's a memorable name! ✨",
                "Excellent branding choice! 💫"
            ],
            "industry": [
                "Fascinating industry! 🚀",
                "That's an exciting sector! 💡",
                "Great field to be in! 🌟",
                "I love working with companies in that space! ⭐"
            ],
            "project_type": [
                "That sounds incredibly promising! 🎯",
                "What an exciting project direction! 🚀",
                "I'm already seeing the potential! ✨",
                "That's exactly the kind of innovation businesses need! 💡"
            ],
            "budget": [
                "Smart investment thinking! 💰",
                "That shows serious commitment to success! 💎",
                "Excellent budget planning! 📈",
                "That's a strategic investment level! 🎯"
            ]
        }
        
        return random.choice(responses.get(context, self.encouragement_phrases))
    
    def get_transition_phrase(self):
        """Get a natural transition phrase"""
        return random.choice(self.transition_phrases)
    
    def personalize_question(self, base_question, company_name=None, industry=None):
        """Personalize questions based on known information"""
        if company_name and "{company}" in base_question:
            return base_question.replace("{company}", company_name)
        
        if industry:
            industry_specific = {
                "technology": "In the fast-paced tech world,",
                "finance": "In the financial sector,",
                "healthcare": "In healthcare innovation,",
                "retail": "In today's retail landscape,",
                "manufacturing": "In modern manufacturing,"
            }
            
            if industry.lower() in industry_specific:
                return f"{industry_specific[industry.lower()]} {base_question.lower()}"
        
        return base_question
    
    def generate_assessment_summary(self, company_data):
        """Generate personalized assessment summary"""
        company_name = company_data.get('company_name', 'your company')
        industry = company_data.get('industry', '')
        project_type = company_data.get('project_type', '')
        
        summaries = [
            f"I'm impressed by {company_name}'s vision! This {project_type} project in the {industry} sector shows real strategic thinking.",
            f"What an exciting opportunity for {company_name}! Your {project_type} initiative could be transformational.",
            f"{company_name} is clearly thinking ahead! This {project_type} project positions you perfectly for growth.",
            f"I love {company_name}'s approach! {project_type} projects like this often become game-changers."
        ]
        
        return random.choice(summaries)
    
    def get_partnership_explanation(self, model_name, company_context):
        """Get personalized partnership model explanation"""
        explanations = {
            "Crisis-to-Success": {
                "intro": "I can sense the urgency in your situation, and that's exactly why the Crisis-to-Success model exists!",
                "benefit": "We specialize in rapid, decisive action when time is critical. Your challenge becomes our mission.",
                "why_perfect": "This model is perfect for you because we detected keywords indicating time-sensitive needs."
            },
            "Performance": {
                "intro": "The Performance model is absolutely perfect for growth-minded companies like yours!",
                "benefit": "We tie our success directly to your results - when you win, we win. It's the ultimate alignment.",
                "why_perfect": "Your company profile shows growth focus, making this results-driven approach ideal."
            },
            "Equity": {
                "intro": "Equity partnerships create the strongest possible alignment between our teams!",
                "benefit": "We become true partners in your long-term success, sharing both risks and rewards.",
                "why_perfect": "For innovative companies with big visions, this creates unmatched commitment."
            },
            "Premium": {
                "intro": "Your project deserves nothing less than our Premium white-glove experience!",
                "benefit": "Dedicated teams, priority support, and meticulous attention to every detail.",
                "why_perfect": "High-value projects like yours benefit from our most comprehensive service level."
            },
            "Monthly Flow": {
                "intro": "Monthly Flow offers the perfect balance of predictability and flexibility!",
                "benefit": "Consistent monthly investments with the agility to adjust as your needs evolve.",
                "why_perfect": "This model suits companies that value steady progress with adaptive planning."
            }
        }
        
        if model_name in explanations:
            exp = explanations[model_name]
            return f"{exp['intro']}\n\n{exp['benefit']}\n\n💡 **Why it's perfect for you**: {exp['why_perfect']}"
        
        return f"The {model_name} model is specifically designed for companies with your profile and needs!"
    
    def get_closing_enthusiasm(self, company_name, project_type):
        """Generate enthusiastic closing message"""
        closings = [
            f"I'm genuinely excited about {company_name}'s {project_type} journey! The potential here is remarkable! 🚀",
            f"What an incredible opportunity for {company_name}! This {project_type} project could be transformational! ✨",
            f"I can already envision the success {company_name} will achieve with this {project_type} initiative! 🌟",
            f"The strategic thinking behind {company_name}'s {project_type} approach is impressive! 💡"
        ]
        
        return random.choice(closings)

# Usage example for integration
def enhance_conversation_response(base_response, context_data):
    """Enhance any response with personality and context"""
    enhancer = ConversationEnhancer()
    
    # Add encouraging phrase
    encouragement = enhancer.get_encouraging_response(context_data.get('type', 'general'))
    
    # Add transition if needed
    if context_data.get('needs_transition'):
        transition = enhancer.get_transition_phrase()
        return f"{encouragement} {transition} {base_response}"
    
    return f"{encouragement} {base_response}"

# Example usage:
if __name__ == "__main__":
    enhancer = ConversationEnhancer()
    
    # Test personalized greeting
    print("Morning Greeting:", enhancer.get_personalized_greeting("morning"))
    print()
    
    # Test encouraging responses
    print("Company Response:", enhancer.get_encouraging_response("company_name"))
    print("Industry Response:", enhancer.get_encouraging_response("industry"))
    print()
    
    # Test partnership explanation
    print("Partnership Explanation:")
    print(enhancer.get_partnership_explanation("Performance", {"growth_focused": True}))
    print()
    
    # Test assessment summary
    company_data = {
        "company_name": "TechStart",
        "industry": "technology",
        "project_type": "software development"
    }
    print("Assessment Summary:", enhancer.generate_assessment_summary(company_data))
