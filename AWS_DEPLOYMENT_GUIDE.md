# 🚀 ZIRA AWS Deployment Guide for techpulse.consulting

## 🎯 **Deployment Architecture**

```
techpulse.consulting (Main Website)
├── CloudFlare DNS
├── AWS Free Tier EC2 Instance
│   ├── ZIRA Production Server (Port 8000)
│   ├── Nginx Reverse Proxy (Port 80/443)
│   └── SSL Certificate (Let's Encrypt)
└── Subdomain: zira.techpulse.consulting
```

## 📋 **AWS Free Tier Setup (Step-by-Step)**

### **Step 1: AWS EC2 Instance Creation**

1. **Login to AWS Console**
   - Go to AWS Console → EC2
   - Select "Launch Instance"

2. **Instance Configuration**
   ```
   AMI: Ubuntu Server 22.04 LTS (Free tier eligible)
   Instance Type: t2.micro (Free tier eligible)
   Storage: 8 GB gp2 (Free tier eligible)
   Security Group: Create new with these rules:
   - SSH (22) - Your IP only
   - HTTP (80) - Anywhere
   - HTTPS (443) - Anywhere
   - Custom TCP (8000) - Anywhere (for testing)
   ```

3. **Key Pair**
   - Create new key pair: `zira-techpulse-key.pem`
   - Download and save securely

### **Step 2: CloudFlare DNS Configuration**

1. **Login to CloudFlare**
   - Go to your techpulse.consulting domain

2. **Add DNS Record**
   ```
   Type: A
   Name: zira
   Content: [Your AWS EC2 Public IP]
   TTL: Auto
   Proxy Status: Proxied (Orange Cloud)
   ```

3. **SSL/TLS Settings**
   ```
   SSL/TLS Mode: Full (strict)
   Always Use HTTPS: On
   ```

### **Step 3: Server Setup**

1. **Connect to EC2 Instance**
   ```bash
   chmod 400 zira-techpulse-key.pem
   ssh -i zira-techpulse-key.pem ubuntu@[EC2-PUBLIC-IP]
   ```

2. **Update System**
   ```bash
   sudo apt update && sudo apt upgrade -y
   ```

3. **Install Python and Dependencies**
   ```bash
   sudo apt install python3 python3-pip python3-venv nginx git -y
   ```

4. **Install Certbot for SSL**
   ```bash
   sudo apt install certbot python3-certbot-nginx -y
   ```

### **Step 4: ZIRA Installation**

1. **Create Project Directory**
   ```bash
   mkdir /home/<USER>/zira-chatbot
   cd /home/<USER>/zira-chatbot
   ```

2. **Upload ZIRA Files**
   ```bash
   # Option A: Using SCP from your local machine
   scp -i zira-techpulse-key.pem -r /path/to/zira-project/* ubuntu@[EC2-IP]:/home/<USER>/zira-chatbot/
   
   # Option B: Using Git (if you have a repository)
   git clone [your-zira-repository] .
   ```

3. **Create Virtual Environment**
   ```bash
   python3 -m venv zira_env
   source zira_env/bin/activate
   pip install -r requirements.txt
   ```

4. **Test ZIRA**
   ```bash
   python production_server.py --port 8000
   # Test: curl http://localhost:8000/health
   ```

### **Step 5: Nginx Configuration**

1. **Create Nginx Config**
   ```bash
   sudo nano /etc/nginx/sites-available/zira.techpulse.consulting
   ```

2. **Add Configuration**
   ```nginx
   server {
       listen 80;
       server_name zira.techpulse.consulting;
       
       location / {
           proxy_pass http://localhost:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           
           # WebSocket support
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";
       }
       
       location /health {
           proxy_pass http://localhost:8000/health;
           access_log off;
       }
   }
   ```

3. **Enable Site**
   ```bash
   sudo ln -s /etc/nginx/sites-available/zira.techpulse.consulting /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl restart nginx
   ```

### **Step 6: SSL Certificate**

1. **Generate SSL Certificate**
   ```bash
   sudo certbot --nginx -d zira.techpulse.consulting
   ```

2. **Auto-renewal Setup**
   ```bash
   sudo crontab -e
   # Add this line:
   0 12 * * * /usr/bin/certbot renew --quiet
   ```

### **Step 7: ZIRA Service Setup**

1. **Create Systemd Service**
   ```bash
   sudo nano /etc/systemd/system/zira.service
   ```

2. **Service Configuration**
   ```ini
   [Unit]
   Description=ZIRA Chatbot Service
   After=network.target

   [Service]
   Type=simple
   User=ubuntu
   WorkingDirectory=/home/<USER>/zira-chatbot
   Environment=PATH=/home/<USER>/zira-chatbot/zira_env/bin
   ExecStart=/home/<USER>/zira-chatbot/zira_env/bin/python production_server.py --port 8000
   Restart=always
   RestartSec=10

   [Install]
   WantedBy=multi-user.target
   ```

3. **Enable and Start Service**
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl enable zira
   sudo systemctl start zira
   sudo systemctl status zira
   ```

## 🌐 **Website Integration**

### **For techpulse.consulting Main Website**

1. **Add Widget to Footer**
   - Copy content from `web/widget_integration.html`
   - Update ZIRA_API_URL to: `https://zira.techpulse.consulting/webhooks/rest/webhook`
   - Add before closing `</body>` tag

2. **Test Integration**
   - Visit your main website
   - Look for floating chat button in bottom-right
   - Test conversation flow

## 💰 **AWS Free Tier Limits**

### **What's Included (12 months free):**
- **EC2**: 750 hours/month of t2.micro instances
- **Storage**: 30 GB of EBS storage
- **Data Transfer**: 15 GB outbound per month
- **Load Balancer**: Not needed for single instance

### **Estimated Monthly Costs After Free Tier:**
- **t2.micro EC2**: ~$8.50/month
- **8 GB Storage**: ~$0.80/month
- **Data Transfer**: ~$0.09/GB after 15 GB
- **Total**: ~$10-15/month

## 🔧 **Monitoring & Maintenance**

### **Health Monitoring**
```bash
# Check ZIRA service
sudo systemctl status zira

# Check logs
sudo journalctl -u zira -f

# Check Nginx
sudo systemctl status nginx

# Check SSL certificate
sudo certbot certificates
```

### **Performance Monitoring**
```bash
# Server resources
htop
df -h
free -h

# ZIRA specific
curl https://zira.techpulse.consulting/health
curl https://zira.techpulse.consulting/status
```

### **Backup Strategy**
```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d)
tar -czf /home/<USER>/backups/zira-backup-$DATE.tar.gz /home/<USER>/zira-chatbot
find /home/<USER>/backups -name "zira-backup-*.tar.gz" -mtime +7 -delete
```

## 🚀 **Go-Live Checklist**

### **Pre-Launch Testing**
- [ ] EC2 instance running and accessible
- [ ] DNS pointing to correct IP
- [ ] SSL certificate installed and working
- [ ] ZIRA service running automatically
- [ ] Health check endpoint responding
- [ ] Chat widget appearing on website
- [ ] Full conversation flow working
- [ ] Mobile responsiveness tested

### **Launch Day**
- [ ] Monitor server resources
- [ ] Check conversation logs
- [ ] Test from different devices/networks
- [ ] Monitor CloudFlare analytics
- [ ] Backup current configuration

### **Post-Launch**
- [ ] Set up monitoring alerts
- [ ] Schedule regular backups
- [ ] Plan capacity scaling if needed
- [ ] Gather user feedback
- [ ] Monitor conversation quality

## 🔒 **Security Best Practices**

1. **Server Security**
   - Keep system updated
   - Use SSH keys only (disable password auth)
   - Configure firewall (ufw)
   - Regular security patches

2. **Application Security**
   - Rate limiting on API endpoints
   - Input validation and sanitization
   - HTTPS only communication
   - Regular dependency updates

3. **CloudFlare Security**
   - Enable DDoS protection
   - Configure Web Application Firewall
   - Monitor security events
   - Use Bot Fight Mode

## 📊 **Success Metrics**

Track these metrics to measure ZIRA's performance:

- **Uptime**: Target 99.9%
- **Response Time**: <2 seconds
- **Conversation Completion**: >70%
- **User Satisfaction**: Monitor feedback
- **Lead Generation**: Assessments → Consultations

---

**🎉 Your ZIRA deployment on AWS Free Tier is ready!**

**Final URLs:**
- **ZIRA Chat**: https://zira.techpulse.consulting
- **Health Check**: https://zira.techpulse.consulting/health
- **Main Website**: https://techpulse.consulting (with floating widget)

For technical support or scaling needs, refer to AWS documentation or contact your development team.
