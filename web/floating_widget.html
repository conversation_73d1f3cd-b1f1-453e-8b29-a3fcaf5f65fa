<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZIRA Floating Chat Widget</title>
    <style>
        /* Floating Chat Widget Styles */
        #zira-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 9999;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Chat Button */
        #zira-chat-button {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            position: relative;
            overflow: hidden;
        }

        #zira-chat-button:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
        }

        #zira-chat-button.active {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }

        /* Notification Badge */
        #zira-notification {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: #ff4757;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        /* Chat Window */
        #zira-chat-window {
            position: absolute;
            bottom: 80px;
            right: 0;
            width: 380px;
            height: 500px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
            display: none;
            flex-direction: column;
            overflow: hidden;
            transform: scale(0.8);
            opacity: 0;
            transition: all 0.3s ease;
        }

        #zira-chat-window.show {
            display: flex;
            transform: scale(1);
            opacity: 1;
        }

        /* Chat Header */
        .zira-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .zira-header-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .zira-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .zira-status {
            font-size: 12px;
            opacity: 0.9;
        }

        .zira-close {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: background 0.3s;
        }

        .zira-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* Chat Messages */
        .zira-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .zira-message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .zira-message.user {
            justify-content: flex-end;
        }

        .zira-message.bot {
            justify-content: flex-start;
        }

        .zira-message-content {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.4;
        }

        .zira-message.user .zira-message-content {
            background: #667eea;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .zira-message.bot .zira-message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 4px;
        }

        /* Quick Actions */
        .zira-quick-actions {
            padding: 10px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .zira-quick-action {
            padding: 6px 12px;
            background: white;
            border: 1px solid #667eea;
            color: #667eea;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .zira-quick-action:hover {
            background: #667eea;
            color: white;
        }

        /* Chat Input */
        .zira-input-container {
            padding: 15px 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .zira-input {
            flex: 1;
            padding: 10px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 20px;
            outline: none;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .zira-input:focus {
            border-color: #667eea;
        }

        .zira-send {
            width: 40px;
            height: 40px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s;
        }

        .zira-send:hover {
            background: #5a6fd8;
        }

        .zira-send:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        /* Typing Indicator */
        .zira-typing {
            display: none;
            padding: 10px 16px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 18px;
            border-bottom-left-radius: 4px;
            max-width: 80%;
            margin-bottom: 15px;
        }

        .zira-typing-dots {
            display: flex;
            gap: 4px;
        }

        .zira-typing-dots span {
            width: 8px;
            height: 8px;
            background: #999;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .zira-typing-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .zira-typing-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            #zira-chat-window {
                width: 320px;
                height: 450px;
                bottom: 70px;
                right: 10px;
            }
            
            #zira-widget {
                bottom: 15px;
                right: 15px;
            }
        }

        /* Welcome Animation */
        .zira-welcome {
            animation: slideInUp 0.5s ease-out;
        }

        @keyframes slideInUp {
            from {
                transform: translateY(20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <!-- Floating Chat Widget -->
    <div id="zira-widget">
        <!-- Chat Button -->
        <button id="zira-chat-button" onclick="toggleZiraChat()">
            <span id="zira-button-icon">💬</span>
            <div id="zira-notification" style="display: none;">1</div>
        </button>

        <!-- Chat Window -->
        <div id="zira-chat-window">
            <!-- Header -->
            <div class="zira-header">
                <div class="zira-header-info">
                    <div class="zira-avatar">🤖</div>
                    <div>
                        <div style="font-weight: bold;">ZIRA</div>
                        <div class="zira-status">AI Strategic Assistant</div>
                    </div>
                </div>
                <button class="zira-close" onclick="closeZiraChat()">×</button>
            </div>

            <!-- Messages -->
            <div class="zira-messages" id="zira-messages">
                <div class="zira-message bot zira-welcome">
                    <div class="zira-message-content">
                        Hello! 👋 I'm ZIRA, your AI strategist from Techpulse Consulting. I'm here to help you explore your project's potential and provide strategic insights. How can I assist you today?
                    </div>
                </div>
            </div>

            <!-- Typing Indicator -->
            <div class="zira-typing" id="zira-typing">
                <div class="zira-typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="zira-quick-actions">
                <div class="zira-quick-action" onclick="sendQuickMessage('Start assessment')">🎯 Start Assessment</div>
                <div class="zira-quick-action" onclick="sendQuickMessage('Partnership models')">🤝 Partnership Models</div>
                <div class="zira-quick-action" onclick="sendQuickMessage('About Techpulse')">ℹ️ About Us</div>
            </div>

            <!-- Input -->
            <div class="zira-input-container">
                <input type="text" class="zira-input" id="zira-input" placeholder="Type your message..." onkeypress="handleZiraKeyPress(event)">
                <button class="zira-send" id="zira-send" onclick="sendZiraMessage()">
                    <span>➤</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // ZIRA Chat Widget JavaScript
        let ziraIsOpen = false;
        let ziraConversationId = 'user_' + Date.now();
        
        // Configuration - Update this URL for production
        const ZIRA_API_URL = '/webhooks/rest/webhook'; // Relative URL for same domain
        
        function toggleZiraChat() {
            const chatWindow = document.getElementById('zira-chat-window');
            const chatButton = document.getElementById('zira-chat-button');
            const buttonIcon = document.getElementById('zira-button-icon');
            const notification = document.getElementById('zira-notification');
            
            if (ziraIsOpen) {
                closeZiraChat();
            } else {
                openZiraChat();
            }
        }
        
        function openZiraChat() {
            const chatWindow = document.getElementById('zira-chat-window');
            const chatButton = document.getElementById('zira-chat-button');
            const buttonIcon = document.getElementById('zira-button-icon');
            const notification = document.getElementById('zira-notification');
            
            chatWindow.classList.add('show');
            chatButton.classList.add('active');
            buttonIcon.textContent = '×';
            notification.style.display = 'none';
            ziraIsOpen = true;
            
            // Focus on input
            setTimeout(() => {
                document.getElementById('zira-input').focus();
            }, 300);
        }
        
        function closeZiraChat() {
            const chatWindow = document.getElementById('zira-chat-window');
            const chatButton = document.getElementById('zira-chat-button');
            const buttonIcon = document.getElementById('zira-button-icon');
            
            chatWindow.classList.remove('show');
            chatButton.classList.remove('active');
            buttonIcon.textContent = '💬';
            ziraIsOpen = false;
        }
        
        function addZiraMessage(content, isUser = false) {
            const messagesContainer = document.getElementById('zira-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `zira-message ${isUser ? 'user' : 'bot'}`;
            
            const messageContent = document.createElement('div');
            messageContent.className = 'zira-message-content';
            messageContent.innerHTML = content.replace(/\n/g, '<br>');
            
            messageDiv.appendChild(messageContent);
            messagesContainer.appendChild(messageDiv);
            
            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function showZiraTyping() {
            const typingIndicator = document.getElementById('zira-typing');
            typingIndicator.style.display = 'block';
            
            const messagesContainer = document.getElementById('zira-messages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function hideZiraTyping() {
            const typingIndicator = document.getElementById('zira-typing');
            typingIndicator.style.display = 'none';
        }
        
        async function sendMessageToZira(message) {
            try {
                const response = await fetch(ZIRA_API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sender: ziraConversationId,
                        message: message
                    })
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }

                const data = await response.json();
                return data;
            } catch (error) {
                console.error('Error:', error);
                return [{
                    text: "I'm sorry, I'm having trouble connecting right now. Please try again in a moment or contact Techpulse Consulting directly at techpulse.consulting"
                }];
            }
        }
        
        async function sendZiraMessage() {
            const input = document.getElementById('zira-input');
            const sendButton = document.getElementById('zira-send');
            const message = input.value.trim();
            
            if (!message) return;

            // Add user message
            addZiraMessage(message, true);
            input.value = '';
            sendButton.disabled = true;

            // Show typing indicator
            showZiraTyping();

            // Send to ZIRA
            const responses = await sendMessageToZira(message);
            
            // Hide typing indicator
            hideZiraTyping();

            // Add bot responses
            responses.forEach(response => {
                if (response.text) {
                    addZiraMessage(response.text);
                }
            });

            sendButton.disabled = false;
            input.focus();
        }
        
        function sendQuickMessage(message) {
            const input = document.getElementById('zira-input');
            input.value = message;
            sendZiraMessage();
        }
        
        function handleZiraKeyPress(event) {
            if (event.key === 'Enter') {
                sendZiraMessage();
            }
        }
        
        // Show notification after 5 seconds if chat hasn't been opened
        setTimeout(() => {
            if (!ziraIsOpen) {
                const notification = document.getElementById('zira-notification');
                notification.style.display = 'flex';
            }
        }, 5000);
        
        // Auto-open chat on first visit (optional)
        // setTimeout(() => {
        //     if (!ziraIsOpen) {
        //         openZiraChat();
        //     }
        // }, 3000);
    </script>
</body>
</html>
