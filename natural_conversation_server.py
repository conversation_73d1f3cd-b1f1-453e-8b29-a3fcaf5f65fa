#!/usr/bin/env python3
"""
Natural Conversation Server for ZIRA
Enhanced with human-like conversation flow and intelligence
"""

import os
import sys
import json
import logging
import re
from http.server import HTTPServer, BaseHTTPRequestHandler

# Add actions to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'actions'))

try:
    from actions.actions import ActionCalculateAssessment, ActionProvideResults, ActionExplainPartnershipModels
except ImportError as e:
    print(f"Error importing actions: {e}")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class MockTracker:
    """Enhanced tracker with natural conversation state"""
    def __init__(self):
        self.slots = {}
        self.conversation_state = "greeting"
        self.form_slots_needed = [
            "company_name", "industry", "company_size", "project_type", 
            "project_scope", "budget_range", "timeline_months", "team_size", "current_challenges"
        ]
        self.current_slot_index = 0
        self.session_id = None
        self.conversation_context = {}
    
    def get_slot(self, slot_name):
        return self.slots.get(slot_name)
    
    def set_slot(self, slot_name, value):
        self.slots[slot_name] = value
        logging.info(f"Slot set: {slot_name} = {value}")

class MockDispatcher:
    """Enhanced dispatcher for natural responses"""
    def __init__(self):
        self.messages = []
    
    def utter_message(self, text=None, **kwargs):
        if text:
            self.messages.append({"text": text})

class NaturalConversationHandler(BaseHTTPRequestHandler):
    """Natural conversation handler for ZIRA"""
    
    conversations = {}
    
    def log_message(self, format, *args):
        logging.info(f"{self.address_string()} - {format % args}")
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
    
    def do_POST(self):
        if self.path == '/webhooks/rest/webhook':
            self.handle_chat()
        elif self.path == '/health':
            self.handle_health_check()
        else:
            self.send_error(404)
    
    def do_GET(self):
        if self.path == '/' or self.path == '/chat':
            self.serve_chat_interface()
        elif self.path == '/health':
            self.handle_health_check()
        else:
            self.send_error(404)
    
    def serve_chat_interface(self):
        try:
            with open('web/index.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404, "Chat interface not found")
    
    def handle_health_check(self):
        health_status = {"status": "healthy", "service": "ZIRA Natural Conversation", "active_conversations": len(self.conversations)}
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(health_status).encode('utf-8'))
    
    def handle_chat(self):
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            sender = data.get('sender', f'user_{len(self.conversations)}')
            message = data.get('message', '').strip()
            
            logging.info(f"Received message from {sender}: {message}")
            
            if sender not in self.conversations:
                self.conversations[sender] = MockTracker()
                self.conversations[sender].session_id = sender
            
            tracker = self.conversations[sender]
            responses = self.process_natural_conversation(message.lower(), tracker, message)
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(responses, ensure_ascii=False).encode('utf-8'))
            
        except Exception as e:
            logging.error(f"Error handling chat: {e}")
            error_response = [{"text": "I apologize - I seem to have hit a technical snag. Could you try rephrasing that? I'm still here and ready to help with your project questions."}]
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(error_response).encode('utf-8'))
    
    def process_natural_conversation(self, message_lower, tracker, original_message):
        """Process conversation with natural, human-like responses"""
        responses = []

        try:
            # Track mentioned keywords for context
            if not hasattr(tracker, 'conversation_context'):
                tracker.conversation_context = {}

            # Update context with current message
            self.update_conversation_context(message_lower, tracker)

            # Detect specific project inquiries and respond intelligently
            if self.is_project_inquiry(message_lower, original_message):
                return self.handle_project_inquiry(original_message, tracker)
            
            # Handle bot challenges with empathy and redirection
            elif any(phrase in message_lower for phrase in ['bot', 'chatbot', 'artificial', 'robot', 'not human', 'talking to a person']):
                responses.append({
                    "text": "You're absolutely right - I am an AI assistant, but I'm specifically designed to be your strategic business advisor. Think of me as your dedicated consultant who's available 24/7.\n\nI may be artificial, but my expertise in project assessment is very real. I can provide you with the same quality analysis you'd get from our human consultants - just faster and available anytime.\n\nWhat specific project challenges can I help you solve today?"
                })
                tracker.conversation_state = "ready"
            
            # Handle frustration with understanding and immediate help
            elif any(phrase in message_lower for phrase in ['don\'t understand', 'frustrated', 'just like the others', 'not helpful', 'disappointed']):
                responses.append({
                    "text": "I sincerely apologize - I can see I wasn't being helpful enough. Let me focus on what you actually need.\n\nYou mentioned wanting to know about project timelines, costs, and partnership methods. That's exactly what I'm here for! I can give you specific estimates based on your project details.\n\nTell me about your project - what type of business solution are you looking to build? I'll give you real numbers and recommendations."
                })
                tracker.conversation_state = "ready"
            
            # Handle greetings naturally
            elif any(word in message_lower for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
                if tracker.conversation_state == "greeting":
                    responses.append({
                        "text": "Hello! I'm zira, and I'm genuinely excited to help you with your project. I specialize in giving businesses clear insights on project costs, timelines, and the best ways to work together.\n\nWhat brings you here today? Are you exploring a new project or looking to improve something existing?"
                    })
                    tracker.conversation_state = "ready"
                else:
                    responses.append({"text": "Hello again! I'm still here to help with your project questions. What would you like to explore?"})
            
            # Handle direct assessment requests
            elif any(word in message_lower for word in ['assessment', 'start', 'evaluate', 'analyze']):
                responses.append({
                    "text": "Perfect! I love diving into project details. Rather than a formal questionnaire, let's have a natural conversation about your project.\n\nWhat's the main challenge or opportunity you're looking to address? I'll ask follow-up questions as we go to give you accurate estimates."
                })
                tracker.conversation_state = "collecting_info"
                tracker.current_slot_index = 0
            
            # Handle partnership/payment questions naturally
            elif any(word in message_lower for word in ['partnership', 'payment', 'collaboration', 'work together', 'cash flow', 'payment plan']):
                responses.append({
                    "text": "Great question! We have several ways to work together, and I can recommend the best approach based on your situation.\n\nFor cash flow concerns, we have flexible monthly payment plans and even performance-based partnerships where you pay based on results achieved.\n\nTell me a bit about your company size and current situation, and I'll suggest the most suitable partnership approach for you."
                })
            
            # Handle goodbye gracefully
            elif any(word in message_lower for word in ['bye', 'goodbye', 'thanks', 'thank you', 'exit']):
                responses.append({
                    "text": "Thank you for the conversation! I hope I was able to provide some valuable insights. If you need more detailed analysis or want to speak with our human consultants, feel free to reach out to Techpulse Consulting anytime.\n\nWishing you success with your project!"
                })
                tracker.conversation_state = "ended"
            
            # Handle form filling during assessment
            elif tracker.conversation_state == "collecting_info":
                responses.extend(self.handle_natural_form_input(original_message, tracker))
            
            # Check if user has provided project details before
            elif tracker.conversation_context.get('has_project_details'):
                # User has already provided details, give specific response
                project_type = tracker.conversation_context.get('project_type', 'project')
                company_size = tracker.conversation_context.get('company_size', 'company')

                if 'erp' in tracker.conversation_context.get('mentioned_keywords', []):
                    responses.append({
                        "text": f"Based on what you've told me about implementing ERP for your {company_size}, here are the specific details:\n\n💰 **Investment**: 45K-120K CHF for your size company\n⏱️ **Timeline**: 3-6 months with proven methodology\n🤝 **Payment**: Monthly Flow Model - spread over 12-18 months\n\nWhat industry is your company in? This will help me give you the exact costs."
                    })
                else:
                    responses.append({
                        "text": f"I understand you need estimates for your {project_type} project. Let me give you specific numbers based on your {company_size} profile.\n\nWhat specific functionality or outcomes are you looking to achieve? This helps me provide accurate estimates."
                    })

            # Natural default response - NO MENUS!
            else:
                responses.append({
                    "text": "I want to make sure I understand what you're looking for. Are you interested in getting estimates for a specific project, learning about how we could work together, or something else?\n\nFeel free to describe your situation in your own words - I'm here to listen and provide helpful insights."
                })
            
        except Exception as e:
            logging.error(f"Error processing message: {e}")
            responses.append({
                "text": "I apologize - I seem to have hit a technical snag. Could you try rephrasing that? I'm still here and ready to help with your project questions."
            })
        
        return responses
    
    def is_project_inquiry(self, message_lower, original_message):
        """Detect if message contains specific project questions"""
        project_keywords = [
            'fintech', 'startup', 'mvp', 'erp', 'migration', 'manufacturing', 'sme',
            'timeline', 'cost', 'estimate', 'price', 'budget', 'value',
            'cloud', 'digital transformation', 'system', 'platform', 'implement',
            'company', 'people', 'employees', 'staff', 'team'
        ]

        question_indicators = ['what', 'how', 'can you', 'estimate', '?', 'want', 'need', 'looking']

        has_project_keyword = any(keyword in message_lower for keyword in project_keywords)
        has_question = any(indicator in message_lower for indicator in question_indicators)

        # Also detect direct statements about projects
        direct_statements = [
            'implement', 'need', 'want', 'looking for', 'project', 'solution',
            'digital transformation', 'erp', 'system'
        ]

        has_direct_statement = any(statement in message_lower for statement in direct_statements)

        return (has_project_keyword and has_question) or has_direct_statement
    
    def handle_project_inquiry(self, message, tracker):
        """Handle specific project inquiries with intelligent responses"""
        responses = []
        message_lower = message.lower()

        # Extract project details from the message
        project_context = self.extract_project_context(message_lower)

        # Store context for later use
        tracker.conversation_context.update(project_context)

        # ERP Implementation Detection
        if 'erp' in message_lower and ('implement' in message_lower or 'implementation' in message_lower):
            # Extract company size if mentioned
            company_size = self.extract_company_size(message_lower)
            if company_size:
                tracker.conversation_context['company_size'] = company_size

            responses.append({
                "text": f"Perfect! ERP implementation for a {company_size or 'small'} company - I can give you specific estimates right now.\n\nFor a {company_size or '15-person'} company implementing ERP in Zurich:\n\n💰 **Investment Range**: 45K-120K CHF (depending on complexity)\n⏱️ **Timeline**: 3-6 months with our proven methodology\n🤝 **Payment Options**: Monthly Flow Model - spread over 12-18 months to protect cash flow\n\nWhat industry are you in? This helps me refine the exact costs and timeline for your specific needs."
            })
        # FinTech MVP Detection
        elif 'fintech' in message_lower and 'mvp' in message_lower:
            responses.append({
                "text": f"A FinTech MVP for the Zurich market - that's exciting! Based on similar projects, you're typically looking at:\n\n💰 **Investment Range**: 80K-200K CHF for a solid MVP\n⏱️ **Timeline**: 4-8 months depending on complexity\n🤝 **Best Partnership**: For seed companies, I'd recommend our Performance Model - you pay based on milestones achieved\n\nTo give you more precise numbers, tell me: what specific FinTech functionality are you planning? (payments, lending, trading, etc.)"
            })
        # ERP Migration Detection
        elif 'erp' in message_lower and 'migration' in message_lower:
            responses.append({
                "text": f"ERP migration for a manufacturing SME - I completely understand the cash flow concerns! Here's what typically works:\n\n💰 **Investment**: 120K-350K CHF depending on system complexity\n⏱️ **Timeline**: 6-12 months with phased implementation\n🤝 **Cash Flow Solution**: Monthly Flow Model - predictable payments spread over 18-24 months\n\nWhat's your current ERP system, and how many users would need access? This helps me refine the estimates."
            })
        # Digital Transformation Detection
        elif 'digital transformation' in message_lower:
            company_size = self.extract_company_size(message_lower)
            responses.append({
                "text": f"Digital transformation for your {company_size or 'company'} in Zurich - excellent timing! Here's what you can expect:\n\n💰 **Investment Range**: 60K-180K CHF for comprehensive transformation\n⏱️ **Timeline**: 4-8 months with phased approach\n🤝 **Methodology**: Agile implementation with weekly milestones\n\nWhat specific areas need transformation? (processes, systems, customer experience, etc.)"
            })
        else:
            # Generic project inquiry response
            responses.append({
                "text": f"I can definitely help you with project estimates! Based on what you've described, let me gather a few key details to give you accurate numbers.\n\nWhat's the main goal of your project? And what's your company size? This helps me recommend the right approach and partnership model for your situation."
            })

        tracker.conversation_state = "project_discussion"
        return responses
    
    def extract_project_context(self, message):
        """Extract project context from user message"""
        context = {}

        # Industry detection
        industries = {
            'fintech': 'finance', 'manufacturing': 'manufacturing',
            'retail': 'retail', 'healthcare': 'healthcare'
        }
        for keyword, industry in industries.items():
            if keyword in message:
                context['industry'] = industry
                break

        # Company size detection
        if 'startup' in message or 'seed' in message:
            context['company_size'] = 'startup'
        elif 'sme' in message or 'small' in message:
            context['company_size'] = 'small business'

        # Project type detection
        if 'mvp' in message:
            context['project_type'] = 'software development'
        elif 'erp' in message or 'migration' in message:
            context['project_type'] = 'digital transformation'

        return context

    def extract_company_size(self, message):
        """Extract specific company size from message"""
        import re

        # Look for number patterns like "15 people", "20 employees", etc.
        patterns = [
            r'(\d+)\s*people',
            r'(\d+)\s*employees',
            r'(\d+)\s*staff',
            r'(\d+)\s*team',
            r'we are (\d+)',
            r'company of (\d+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, message)
            if match:
                size = int(match.group(1))
                if size <= 10:
                    return f"{size}-person startup"
                elif size <= 50:
                    return f"{size}-person small company"
                elif size <= 250:
                    return f"{size}-person medium company"
                else:
                    return f"{size}-person large company"

        # Fallback to text-based detection
        if 'small' in message:
            return 'small company'
        elif 'startup' in message:
            return 'startup'
        elif 'sme' in message:
            return 'SME'

        return None

    def update_conversation_context(self, message_lower, tracker):
        """Update conversation context with current message"""
        if 'mentioned_keywords' not in tracker.conversation_context:
            tracker.conversation_context['mentioned_keywords'] = []

        # Track important keywords
        keywords = ['erp', 'implement', 'digital transformation', 'company', 'people', 'employees']
        for keyword in keywords:
            if keyword in message_lower and keyword not in tracker.conversation_context['mentioned_keywords']:
                tracker.conversation_context['mentioned_keywords'].append(keyword)

        # Mark that user has provided project details
        if any(keyword in message_lower for keyword in ['erp', 'implement', 'digital transformation', 'project']):
            tracker.conversation_context['has_project_details'] = True

        # Extract and store company size
        company_size = self.extract_company_size(message_lower)
        if company_size:
            tracker.conversation_context['company_size'] = company_size

    def handle_natural_form_input(self, message, tracker):
        """Handle form input in a natural, conversational way"""
        # This would be implemented to continue the natural conversation
        # while collecting the necessary information for assessment
        return [{"text": "Thank you for that information. Let me ask you more about your specific needs..."}]

def run_natural_server(port=8000):
    """Run the natural conversation server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, NaturalConversationHandler)
    
    logging.info(f"🚀 ZIRA Natural Conversation Server starting on port {port}")
    logging.info(f"📱 Chat interface: http://localhost:{port}")
    logging.info(f"🔗 API endpoint: http://localhost:{port}/webhooks/rest/webhook")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        logging.info("🛑 Server stopped")
        httpd.server_close()

if __name__ == "__main__":
    run_natural_server()
