# 🚀 ZIRA Deployment Guide for Techpulse Consulting

## 📋 Quick Deployment Checklist

### ✅ **Option 1: Simple Odoo Integration (Recommended)**

1. **Upload Files to Your Website**
   - Upload `web/index.html` to your website files
   - Upload `production_server.py` to your server
   - Upload `actions/` folder
   - Upload `requirements.txt`

2. **Install Dependencies on Server**
   ```bash
   pip install -r requirements.txt
   ```

3. **Start ZIRA Server**
   ```bash
   python production_server.py --port 8000
   ```

4. **Add to Your Action Hub Page**
   ```html
   <div class="zira-integration">
       <h3>💬 Chat with ZIRA - AI Strategic Assistant</h3>
       <iframe src="https://your-domain.com:8000" 
               width="100%" 
               height="600px" 
               frameborder="0">
       </iframe>
   </div>
   ```

### ✅ **Option 2: Professional Production Setup**

#### **Step 1: Server Preparation**
- Get a VPS (DigitalOcean, AWS, Linode)
- Install Python 3.9+
- Set up nginx reverse proxy
- Configure SSL certificates

#### **Step 2: Domain Setup**
- Create subdomain: `zira.techpulseconsulting.com`
- Point DNS to your server
- Configure SSL

#### **Step 3: Production Deployment**
```bash
# Clone your project
git clone your-zira-repository
cd zira-chatbot

# Install dependencies
pip install -r requirements.txt

# Run with SSL
python production_server.py --port 8000 --ssl --cert /path/to/cert.pem --key /path/to/key.pem
```

## 🔧 **Odoo-Specific Integration**

### **Method 1: Page Integration**

1. **Go to Website → Pages**
2. **Edit your Action Hub page**
3. **Add HTML block with this code:**

```html
<section class="zira-chat-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <h2>🤖 Meet ZIRA - Your AI Strategic Assistant</h2>
                <p>Get instant project assessments and partnership recommendations</p>
                
                <div class="chat-container" style="height: 600px; border: 1px solid #ddd; border-radius: 10px; overflow: hidden;">
                    <iframe src="/zira-chat" 
                            width="100%" 
                            height="100%" 
                            frameborder="0">
                    </iframe>
                </div>
            </div>
        </div>
    </div>
</section>
```

### **Method 2: Popup Chat Widget**

```html
<!-- Add this to your page footer -->
<div id="zira-chat-widget" style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;">
    <button id="zira-toggle" style="background: #667eea; color: white; border: none; padding: 15px; border-radius: 50px; cursor: pointer;">
        💬 Chat with ZIRA
    </button>
    
    <div id="zira-popup" style="display: none; width: 400px; height: 500px; background: white; border: 1px solid #ddd; border-radius: 10px; position: absolute; bottom: 60px; right: 0;">
        <iframe src="/zira-chat" width="100%" height="100%" frameborder="0"></iframe>
    </div>
</div>

<script>
document.getElementById('zira-toggle').onclick = function() {
    var popup = document.getElementById('zira-popup');
    popup.style.display = popup.style.display === 'none' ? 'block' : 'none';
};
</script>
```

## 🌐 **URL Configuration for Your Website**

### **Current Website:** `https://techpulseconsulting.odoo.com/en-ch/action-hub`

#### **Integration Options:**

1. **Subdirectory:** `https://techpulseconsulting.odoo.com/zira/`
2. **Subdomain:** `https://zira.techpulseconsulting.com/`
3. **Same page:** Embedded directly in action-hub

#### **Recommended Setup:**

```
https://techpulseconsulting.odoo.com/
├── en-ch/action-hub (your main page)
├── zira/ (ZIRA chat interface)
└── api/zira/ (ZIRA backend API)
```

## 🔒 **Security Configuration**

### **CORS Settings**
```python
# In production_server.py, update CORS for your domain:
self.send_header('Access-Control-Allow-Origin', 'https://techpulseconsulting.odoo.com')
```

### **SSL Configuration**
```bash
# Generate SSL certificate (Let's Encrypt)
certbot certonly --standalone -d zira.techpulseconsulting.com

# Run with SSL
python production_server.py --ssl --cert /etc/letsencrypt/live/zira.techpulseconsulting.com/fullchain.pem --key /etc/letsencrypt/live/zira.techpulseconsulting.com/privkey.pem
```

## 📊 **Monitoring & Maintenance**

### **Health Checks**
- Visit: `https://your-domain.com/health`
- Monitor: `https://your-domain.com/status`

### **Log Monitoring**
```bash
# View logs
tail -f zira.log

# Monitor conversations
grep "assessment_complete" zira.log
```

### **Backup Strategy**
- Daily backup of conversation logs
- Weekly backup of configuration files
- Monthly backup of entire system

## 🚀 **Go-Live Checklist**

### **Pre-Launch (Test Everything)**
- [ ] ZIRA responds correctly to greetings
- [ ] Assessment flow works end-to-end
- [ ] Calculations are accurate
- [ ] Partnership recommendations work
- [ ] Error handling functions properly
- [ ] Website integration displays correctly

### **Launch Day**
- [ ] Deploy to production server
- [ ] Update DNS settings
- [ ] Configure SSL certificates
- [ ] Test from external networks
- [ ] Monitor for any issues
- [ ] Announce to your team

### **Post-Launch**
- [ ] Monitor conversation quality
- [ ] Track completion rates
- [ ] Gather user feedback
- [ ] Plan improvements

## 📞 **Support & Troubleshooting**

### **Common Issues**

1. **CORS Errors**
   - Update allowed origins in production_server.py
   - Ensure proper headers are set

2. **Connection Refused**
   - Check if server is running
   - Verify port is open
   - Check firewall settings

3. **SSL Certificate Issues**
   - Verify certificate paths
   - Check certificate expiration
   - Ensure proper permissions

### **Performance Optimization**

1. **Server Resources**
   - Monitor CPU and memory usage
   - Scale server if needed
   - Implement load balancing for high traffic

2. **Response Times**
   - Optimize calculation algorithms
   - Implement caching for common responses
   - Use CDN for static assets

## 🎯 **Success Metrics**

Track these metrics to measure ZIRA's success:

- **Conversation Completion Rate** (target: >70%)
- **Assessment Accuracy** (user feedback)
- **Lead Generation** (assessments → consultations)
- **User Satisfaction** (chat ratings)
- **Response Time** (target: <2 seconds)

---

**🎉 Congratulations! ZIRA is ready to transform your client interactions!**

For technical support or customization requests, refer to the main README.md file or contact your development team.
