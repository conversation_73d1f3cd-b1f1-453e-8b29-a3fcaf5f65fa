#!/usr/bin/env python3
"""
Test script for ZIRA's custom actions
This script tests the business logic without requiring a full Rasa installation
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'actions'))

from actions.actions import ActionCalculateAssessment, ActionProvideResults, ActionExplainPartnershipModels

class MockTracker:
    """Mock tracker for testing"""
    def __init__(self, slots):
        self.slots = slots
    
    def get_slot(self, slot_name):
        return self.slots.get(slot_name)

class MockDispatcher:
    """Mock dispatcher for testing"""
    def __init__(self):
        self.messages = []
    
    def utter_message(self, text=None, **kwargs):
        if text:
            self.messages.append(text)
            print(f"🤖 ZIRA: {text}")

def test_assessment_calculation():
    """Test the assessment calculation logic"""
    print("=" * 60)
    print("🧪 TESTING ZIRA'S ASSESSMENT CALCULATION")
    print("=" * 60)
    
    # Test data - simulating a startup tech company
    test_slots = {
        "company_name": "TechStart",
        "industry": "technology",
        "company_size": "startup",
        "project_type": "software development",
        "project_scope": "mobile app development",
        "budget_range": "50K-200K CHF",
        "timeline_months": 6.0,
        "team_size": 5.0,
        "current_challenges": "need digital presence and market entry"
    }
    
    # Create mock objects
    tracker = MockTracker(test_slots)
    dispatcher = MockDispatcher()
    
    # Test calculation action
    calc_action = ActionCalculateAssessment()
    events = calc_action.run(dispatcher, tracker, {})
    
    print("\n📊 CALCULATION RESULTS:")
    for event in events:
        if event.get('event') == 'slot':
            slot_name = event.get('name')
            slot_value = event.get('value')
            print(f"   {slot_name}: {slot_value}")
    
    # Update tracker with results
    result_slots = test_slots.copy()
    for event in events:
        if event.get('event') == 'slot':
            result_slots[event.get('name')] = event.get('value')
    
    # Test results action
    result_tracker = MockTracker(result_slots)
    results_action = ActionProvideResults()
    results_action.run(dispatcher, result_tracker, {})
    
    return result_slots

def test_partnership_models():
    """Test partnership model explanation"""
    print("\n" + "=" * 60)
    print("🤝 TESTING PARTNERSHIP MODELS")
    print("=" * 60)
    
    dispatcher = MockDispatcher()
    tracker = MockTracker({})
    
    partnership_action = ActionExplainPartnershipModels()
    partnership_action.run(dispatcher, tracker, {})

def test_different_scenarios():
    """Test different business scenarios"""
    print("\n" + "=" * 60)
    print("🎯 TESTING DIFFERENT BUSINESS SCENARIOS")
    print("=" * 60)
    
    scenarios = [
        {
            "name": "Large Enterprise - Digital Transformation",
            "slots": {
                "company_name": "MegaCorp",
                "industry": "finance",
                "company_size": "large corporation",
                "project_type": "digital transformation",
                "project_scope": "enterprise platform migration",
                "budget_range": "500K+ CHF",
                "timeline_months": 18.0,
                "team_size": 15.0,
                "current_challenges": "legacy system modernization"
            }
        },
        {
            "name": "Crisis Situation - Urgent Fix",
            "slots": {
                "company_name": "CrisisCorpCorp",
                "industry": "retail",
                "company_size": "medium enterprise",
                "project_type": "process optimization",
                "project_scope": "critical system recovery",
                "budget_range": "200K-500K CHF",
                "timeline_months": 3.0,
                "team_size": 8.0,
                "current_challenges": "critical system failure urgent fix needed"
            }
        },
        {
            "name": "Small Startup - Limited Budget",
            "slots": {
                "company_name": "StartupDream",
                "industry": "technology",
                "company_size": "startup",
                "project_type": "software development",
                "project_scope": "MVP development",
                "budget_range": "under 50K CHF",
                "timeline_months": 4.0,
                "team_size": 3.0,
                "current_challenges": "limited resources need MVP"
            }
        }
    ]
    
    calc_action = ActionCalculateAssessment()
    results_action = ActionProvideResults()
    
    for scenario in scenarios:
        print(f"\n🏢 SCENARIO: {scenario['name']}")
        print("-" * 40)
        
        tracker = MockTracker(scenario['slots'])
        dispatcher = MockDispatcher()
        
        # Calculate assessment
        events = calc_action.run(dispatcher, tracker, {})
        
        # Update tracker with results
        result_slots = scenario['slots'].copy()
        for event in events:
            if event.get('event') == 'slot':
                result_slots[event.get('name')] = event.get('value')
        
        # Show results
        result_tracker = MockTracker(result_slots)
        results_action.run(dispatcher, result_tracker, {})
        
        print()

def main():
    """Run all tests"""
    print("🚀 ZIRA CHATBOT - BUSINESS LOGIC TEST SUITE")
    print("Techpulse Consulting - AI Strategic Assistant")
    
    try:
        # Test basic assessment
        test_assessment_calculation()
        
        # Test partnership models
        test_partnership_models()
        
        # Test different scenarios
        test_different_scenarios()
        
        print("\n" + "=" * 60)
        print("✅ ALL TESTS COMPLETED SUCCESSFULLY!")
        print("ZIRA's business logic is working correctly.")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        print("Please check the actions/actions.py file for issues.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
