version: "3.1"

intents:
  - greet
  - goodbye
  - affirm
  - deny
  - mood_great
  - mood_unhappy
  - bot_challenge
  - start_assessment
  - provide_company_info
  - provide_project_info
  - provide_budget_info
  - provide_timeline_info
  - provide_team_info
  - request_results
  - ask_partnership_models
  - ask_about_techpulse

entities:
  - company_name
  - industry
  - company_size
  - project_type
  - project_scope
  - budget_range
  - timeline_months
  - team_size
  - current_challenges
  - partnership_preference

slots:
  company_name:
    type: text
    influence_conversation: false
    mappings:
    - type: from_entity
      entity: company_name
  
  industry:
    type: text
    influence_conversation: false
    mappings:
    - type: from_entity
      entity: industry
  
  company_size:
    type: text
    influence_conversation: false
    mappings:
    - type: from_entity
      entity: company_size
  
  project_type:
    type: text
    influence_conversation: false
    mappings:
    - type: from_entity
      entity: project_type
  
  project_scope:
    type: text
    influence_conversation: false
    mappings:
    - type: from_entity
      entity: project_scope
  
  budget_range:
    type: text
    influence_conversation: false
    mappings:
    - type: from_entity
      entity: budget_range
  
  timeline_months:
    type: float
    influence_conversation: false
    mappings:
    - type: from_entity
      entity: timeline_months
  
  team_size:
    type: float
    influence_conversation: false
    mappings:
    - type: from_entity
      entity: team_size
  
  current_challenges:
    type: text
    influence_conversation: false
    mappings:
    - type: from_entity
      entity: current_challenges
  
  partnership_preference:
    type: text
    influence_conversation: false
    mappings:
    - type: from_entity
      entity: partnership_preference
  
  assessment_complete:
    type: bool
    influence_conversation: true
    initial_value: false
    mappings:
    - type: custom

responses:
  utter_greet:
  - text: "Hello! I'm ZIRA, your AI strategist from Techpulse Consulting. I'm here to help you with a preliminary project assessment. How can I assist you today?"

  utter_goodbye:
  - text: "Thank you for using ZIRA! If you need further assistance, feel free to reach out to Techpulse Consulting. Have a great day!"

  utter_iamabot:
  - text: "I'm ZIRA, an AI assistant created by Techpulse Consulting to help with strategic business assessments and project planning."

  utter_start_assessment:
  - text: "Excellent! I'll guide you through a strategic assessment to provide you with preliminary project insights. Let's start with some basic information about your company."

  utter_ask_company_name:
  - text: "What's the name of your company?"

  utter_ask_industry:
  - text: "What industry does your company operate in?"

  utter_ask_company_size:
  - text: "How would you describe your company size? (e.g., startup, small business, medium enterprise, large corporation)"

  utter_ask_project_type:
  - text: "What type of project are you considering? (e.g., digital transformation, software development, process optimization, strategic consulting)"

  utter_ask_project_scope:
  - text: "Could you describe the scope of your project in a few words?"

  utter_ask_budget_range:
  - text: "What's your approximate budget range for this project? (e.g., under 50K CHF, 50K-200K CHF, 200K-500K CHF, 500K+ CHF)"

  utter_ask_timeline:
  - text: "What's your desired timeline for this project in months?"

  utter_ask_team_size:
  - text: "How many team members do you expect to be involved in this project?"

  utter_ask_challenges:
  - text: "What are the main challenges or pain points you're hoping to address with this project?"

  utter_processing_assessment:
  - text: "Thank you for providing all the information! Let me process your assessment and calculate the preliminary results..."

  utter_default:
  - text: "I'm sorry, I didn't understand that. Could you please rephrase or let me know if you'd like to start a project assessment?"

actions:
  - action_calculate_assessment
  - action_provide_results
  - action_explain_partnership_models
  - validate_assessment_form

forms:
  assessment_form:
    required_slots:
      - company_name
      - industry
      - company_size
      - project_type
      - project_scope
      - budget_range
      - timeline_months
      - team_size
      - current_challenges

session_config:
  session_expiration_time: 60
  carry_over_slots_to_new_session: true
