Metadata-Version: 2.1
Name: opentelemetry-exporter-otlp-proto-http
Version: 1.16.0
Summary: OpenTelemetry Collector Protobuf over HTTP Exporter
Project-URL: Homepage, https://github.com/open-telemetry/opentelemetry-python/tree/main/exporter/opentelemetry-exporter-otlp-proto-http
Author-email: OpenTelemetry Authors <<EMAIL>>
License-Expression: Apache-2.0
License-File: LICENSE
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Python: >=3.7
Requires-Dist: backoff<2.0.0,>=1.10.0; python_version < '3.7'
Requires-Dist: backoff<3.0.0,>=1.10.0; python_version >= '3.7'
Requires-Dist: googleapis-common-protos~=1.52
Requires-Dist: opentelemetry-api~=1.15
Requires-Dist: opentelemetry-proto==1.16.0
Requires-Dist: opentelemetry-sdk~=1.16.0
Requires-Dist: requests~=2.7
Provides-Extra: test
Requires-Dist: responses==0.22.0; extra == 'test'
Description-Content-Type: text/x-rst

OpenTelemetry Collector Protobuf over HTTP Exporter
===================================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-exporter-otlp-proto-http.svg
   :target: https://pypi.org/project/opentelemetry-exporter-otlp-proto-http/

This library allows to export data to the OpenTelemetry Collector using the OpenTelemetry Protocol using Protobuf over HTTP.

Installation
------------

::

     pip install opentelemetry-exporter-otlp-proto-http


References
----------

* `OpenTelemetry Collector Exporter <https://opentelemetry-python.readthedocs.io/en/latest/exporter/otlp/otlp.html>`_
* `OpenTelemetry Collector <https://github.com/open-telemetry/opentelemetry-collector/>`_
* `OpenTelemetry <https://opentelemetry.io/>`_
* `OpenTelemetry Protocol Specification <https://github.com/open-telemetry/oteps/blob/main/text/0035-opentelemetry-protocol.md>`_
