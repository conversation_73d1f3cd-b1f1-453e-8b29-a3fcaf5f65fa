Metadata-Version: 2.1
Name: googleapis-common-protos
Version: 1.56.1
Summary: Common protobufs used in Google APIs
Home-page: https://github.com/googleapis/python-api-common-protos
Author: Google LLC
Author-email: <EMAIL>
License: Apache-2.0
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: Implementation :: CPython
Requires-Python: >=3.6
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: protobuf (>=3.15.0)
Provides-Extra: grpc
Requires-Dist: grpcio (>=1.0.0) ; extra == 'grpc'


# Google APIs common protos

[![pypi](https://img.shields.io/pypi/v/googleapis-common-protos.svg)](https://pypi.org/project/googleapis-common-protos/)


googleapis-common-protos contains the python classes generated from the common
protos in the [googleapis/api-common-protos](https://github.com/googleapis/api-common-protos) repository.
