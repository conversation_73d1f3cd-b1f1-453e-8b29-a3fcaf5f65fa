# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: opentelemetry/proto/trace/v1/trace.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from opentelemetry.proto.common.v1 import common_pb2 as opentelemetry_dot_proto_dot_common_dot_v1_dot_common__pb2
from opentelemetry.proto.resource.v1 import resource_pb2 as opentelemetry_dot_proto_dot_resource_dot_v1_dot_resource__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(opentelemetry/proto/trace/v1/trace.proto\x12\x1copentelemetry.proto.trace.v1\x1a*opentelemetry/proto/common/v1/common.proto\x1a.opentelemetry/proto/resource/v1/resource.proto\"Q\n\nTracesData\x12\x43\n\x0eresource_spans\x18\x01 \x03(\x0b\x32+.opentelemetry.proto.trace.v1.ResourceSpans\"\x86\x02\n\rResourceSpans\x12;\n\x08resource\x18\x01 \x01(\x0b\x32).opentelemetry.proto.resource.v1.Resource\x12=\n\x0bscope_spans\x18\x02 \x03(\x0b\x32(.opentelemetry.proto.trace.v1.ScopeSpans\x12\x65\n\x1dinstrumentation_library_spans\x18\xe8\x07 \x03(\x0b\x32\x39.opentelemetry.proto.trace.v1.InstrumentationLibrarySpansB\x02\x18\x01\x12\x12\n\nschema_url\x18\x03 \x01(\t\"\x97\x01\n\nScopeSpans\x12\x42\n\x05scope\x18\x01 \x01(\x0b\x32\x33.opentelemetry.proto.common.v1.InstrumentationScope\x12\x31\n\x05spans\x18\x02 \x03(\x0b\x32\".opentelemetry.proto.trace.v1.Span\x12\x12\n\nschema_url\x18\x03 \x01(\t\"\xc0\x01\n\x1bInstrumentationLibrarySpans\x12V\n\x17instrumentation_library\x18\x01 \x01(\x0b\x32\x35.opentelemetry.proto.common.v1.InstrumentationLibrary\x12\x31\n\x05spans\x18\x02 \x03(\x0b\x32\".opentelemetry.proto.trace.v1.Span\x12\x12\n\nschema_url\x18\x03 \x01(\t:\x02\x18\x01\"\xe6\x07\n\x04Span\x12\x10\n\x08trace_id\x18\x01 \x01(\x0c\x12\x0f\n\x07span_id\x18\x02 \x01(\x0c\x12\x13\n\x0btrace_state\x18\x03 \x01(\t\x12\x16\n\x0eparent_span_id\x18\x04 \x01(\x0c\x12\x0c\n\x04name\x18\x05 \x01(\t\x12\x39\n\x04kind\x18\x06 \x01(\x0e\x32+.opentelemetry.proto.trace.v1.Span.SpanKind\x12\x1c\n\x14start_time_unix_nano\x18\x07 \x01(\x06\x12\x1a\n\x12\x65nd_time_unix_nano\x18\x08 \x01(\x06\x12;\n\nattributes\x18\t \x03(\x0b\x32\'.opentelemetry.proto.common.v1.KeyValue\x12 \n\x18\x64ropped_attributes_count\x18\n \x01(\r\x12\x38\n\x06\x65vents\x18\x0b \x03(\x0b\x32(.opentelemetry.proto.trace.v1.Span.Event\x12\x1c\n\x14\x64ropped_events_count\x18\x0c \x01(\r\x12\x36\n\x05links\x18\r \x03(\x0b\x32\'.opentelemetry.proto.trace.v1.Span.Link\x12\x1b\n\x13\x64ropped_links_count\x18\x0e \x01(\r\x12\x34\n\x06status\x18\x0f \x01(\x0b\x32$.opentelemetry.proto.trace.v1.Status\x1a\x8c\x01\n\x05\x45vent\x12\x16\n\x0etime_unix_nano\x18\x01 \x01(\x06\x12\x0c\n\x04name\x18\x02 \x01(\t\x12;\n\nattributes\x18\x03 \x03(\x0b\x32\'.opentelemetry.proto.common.v1.KeyValue\x12 \n\x18\x64ropped_attributes_count\x18\x04 \x01(\r\x1a\x9d\x01\n\x04Link\x12\x10\n\x08trace_id\x18\x01 \x01(\x0c\x12\x0f\n\x07span_id\x18\x02 \x01(\x0c\x12\x13\n\x0btrace_state\x18\x03 \x01(\t\x12;\n\nattributes\x18\x04 \x03(\x0b\x32\'.opentelemetry.proto.common.v1.KeyValue\x12 \n\x18\x64ropped_attributes_count\x18\x05 \x01(\r\"\x99\x01\n\x08SpanKind\x12\x19\n\x15SPAN_KIND_UNSPECIFIED\x10\x00\x12\x16\n\x12SPAN_KIND_INTERNAL\x10\x01\x12\x14\n\x10SPAN_KIND_SERVER\x10\x02\x12\x14\n\x10SPAN_KIND_CLIENT\x10\x03\x12\x16\n\x12SPAN_KIND_PRODUCER\x10\x04\x12\x16\n\x12SPAN_KIND_CONSUMER\x10\x05\"\xae\x01\n\x06Status\x12\x0f\n\x07message\x18\x02 \x01(\t\x12=\n\x04\x63ode\x18\x03 \x01(\x0e\x32/.opentelemetry.proto.trace.v1.Status.StatusCode\"N\n\nStatusCode\x12\x15\n\x11STATUS_CODE_UNSET\x10\x00\x12\x12\n\x0eSTATUS_CODE_OK\x10\x01\x12\x15\n\x11STATUS_CODE_ERROR\x10\x02J\x04\x08\x01\x10\x02\x42X\n\x1fio.opentelemetry.proto.trace.v1B\nTraceProtoP\x01Z\'go.opentelemetry.io/proto/otlp/trace/v1b\x06proto3')



_TRACESDATA = DESCRIPTOR.message_types_by_name['TracesData']
_RESOURCESPANS = DESCRIPTOR.message_types_by_name['ResourceSpans']
_SCOPESPANS = DESCRIPTOR.message_types_by_name['ScopeSpans']
_INSTRUMENTATIONLIBRARYSPANS = DESCRIPTOR.message_types_by_name['InstrumentationLibrarySpans']
_SPAN = DESCRIPTOR.message_types_by_name['Span']
_SPAN_EVENT = _SPAN.nested_types_by_name['Event']
_SPAN_LINK = _SPAN.nested_types_by_name['Link']
_STATUS = DESCRIPTOR.message_types_by_name['Status']
_SPAN_SPANKIND = _SPAN.enum_types_by_name['SpanKind']
_STATUS_STATUSCODE = _STATUS.enum_types_by_name['StatusCode']
TracesData = _reflection.GeneratedProtocolMessageType('TracesData', (_message.Message,), {
  'DESCRIPTOR' : _TRACESDATA,
  '__module__' : 'opentelemetry.proto.trace.v1.trace_pb2'
  # @@protoc_insertion_point(class_scope:opentelemetry.proto.trace.v1.TracesData)
  })
_sym_db.RegisterMessage(TracesData)

ResourceSpans = _reflection.GeneratedProtocolMessageType('ResourceSpans', (_message.Message,), {
  'DESCRIPTOR' : _RESOURCESPANS,
  '__module__' : 'opentelemetry.proto.trace.v1.trace_pb2'
  # @@protoc_insertion_point(class_scope:opentelemetry.proto.trace.v1.ResourceSpans)
  })
_sym_db.RegisterMessage(ResourceSpans)

ScopeSpans = _reflection.GeneratedProtocolMessageType('ScopeSpans', (_message.Message,), {
  'DESCRIPTOR' : _SCOPESPANS,
  '__module__' : 'opentelemetry.proto.trace.v1.trace_pb2'
  # @@protoc_insertion_point(class_scope:opentelemetry.proto.trace.v1.ScopeSpans)
  })
_sym_db.RegisterMessage(ScopeSpans)

InstrumentationLibrarySpans = _reflection.GeneratedProtocolMessageType('InstrumentationLibrarySpans', (_message.Message,), {
  'DESCRIPTOR' : _INSTRUMENTATIONLIBRARYSPANS,
  '__module__' : 'opentelemetry.proto.trace.v1.trace_pb2'
  # @@protoc_insertion_point(class_scope:opentelemetry.proto.trace.v1.InstrumentationLibrarySpans)
  })
_sym_db.RegisterMessage(InstrumentationLibrarySpans)

Span = _reflection.GeneratedProtocolMessageType('Span', (_message.Message,), {

  'Event' : _reflection.GeneratedProtocolMessageType('Event', (_message.Message,), {
    'DESCRIPTOR' : _SPAN_EVENT,
    '__module__' : 'opentelemetry.proto.trace.v1.trace_pb2'
    # @@protoc_insertion_point(class_scope:opentelemetry.proto.trace.v1.Span.Event)
    })
  ,

  'Link' : _reflection.GeneratedProtocolMessageType('Link', (_message.Message,), {
    'DESCRIPTOR' : _SPAN_LINK,
    '__module__' : 'opentelemetry.proto.trace.v1.trace_pb2'
    # @@protoc_insertion_point(class_scope:opentelemetry.proto.trace.v1.Span.Link)
    })
  ,
  'DESCRIPTOR' : _SPAN,
  '__module__' : 'opentelemetry.proto.trace.v1.trace_pb2'
  # @@protoc_insertion_point(class_scope:opentelemetry.proto.trace.v1.Span)
  })
_sym_db.RegisterMessage(Span)
_sym_db.RegisterMessage(Span.Event)
_sym_db.RegisterMessage(Span.Link)

Status = _reflection.GeneratedProtocolMessageType('Status', (_message.Message,), {
  'DESCRIPTOR' : _STATUS,
  '__module__' : 'opentelemetry.proto.trace.v1.trace_pb2'
  # @@protoc_insertion_point(class_scope:opentelemetry.proto.trace.v1.Status)
  })
_sym_db.RegisterMessage(Status)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\037io.opentelemetry.proto.trace.v1B\nTraceProtoP\001Z\'go.opentelemetry.io/proto/otlp/trace/v1'
  _RESOURCESPANS.fields_by_name['instrumentation_library_spans']._options = None
  _RESOURCESPANS.fields_by_name['instrumentation_library_spans']._serialized_options = b'\030\001'
  _INSTRUMENTATIONLIBRARYSPANS._options = None
  _INSTRUMENTATIONLIBRARYSPANS._serialized_options = b'\030\001'
  _TRACESDATA._serialized_start=166
  _TRACESDATA._serialized_end=247
  _RESOURCESPANS._serialized_start=250
  _RESOURCESPANS._serialized_end=512
  _SCOPESPANS._serialized_start=515
  _SCOPESPANS._serialized_end=666
  _INSTRUMENTATIONLIBRARYSPANS._serialized_start=669
  _INSTRUMENTATIONLIBRARYSPANS._serialized_end=861
  _SPAN._serialized_start=864
  _SPAN._serialized_end=1862
  _SPAN_EVENT._serialized_start=1406
  _SPAN_EVENT._serialized_end=1546
  _SPAN_LINK._serialized_start=1549
  _SPAN_LINK._serialized_end=1706
  _SPAN_SPANKIND._serialized_start=1709
  _SPAN_SPANKIND._serialized_end=1862
  _STATUS._serialized_start=1865
  _STATUS._serialized_end=2039
  _STATUS_STATUSCODE._serialized_start=1955
  _STATUS_STATUSCODE._serialized_end=2033
# @@protoc_insertion_point(module_scope)
