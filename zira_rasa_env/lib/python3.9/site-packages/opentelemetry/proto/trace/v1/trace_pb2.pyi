"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
import builtins
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import opentelemetry.proto.common.v1.common_pb2
import opentelemetry.proto.resource.v1.resource_pb2
import typing
import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor = ...

class TracesData(google.protobuf.message.Message):
    """TracesData represents the traces data that can be stored in a persistent storage,
    OR can be embedded by other protocols that transfer OTLP traces data but do
    not implement the OTLP protocol.

    The main difference between this message and collector protocol is that
    in this message there will not be any "control" or "metadata" specific to
    OTLP protocol.

    When new fields are added into this message, the OTLP request MUST be updated
    as well.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    RESOURCE_SPANS_FIELD_NUMBER: builtins.int
    @property
    def resource_spans(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ResourceSpans]:
        """An array of ResourceSpans.
        For data coming from a single resource this array will typically contain
        one element. Intermediary nodes that receive data from multiple origins
        typically batch the data before forwarding further and in that case this
        array will contain multiple elements.
        """
        pass
    def __init__(self,
        *,
        resource_spans : typing.Optional[typing.Iterable[global___ResourceSpans]] = ...,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["resource_spans",b"resource_spans"]) -> None: ...
global___TracesData = TracesData

class ResourceSpans(google.protobuf.message.Message):
    """A collection of ScopeSpans from a Resource."""
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    RESOURCE_FIELD_NUMBER: builtins.int
    SCOPE_SPANS_FIELD_NUMBER: builtins.int
    INSTRUMENTATION_LIBRARY_SPANS_FIELD_NUMBER: builtins.int
    SCHEMA_URL_FIELD_NUMBER: builtins.int
    @property
    def resource(self) -> opentelemetry.proto.resource.v1.resource_pb2.Resource:
        """The resource for the spans in this message.
        If this field is not set then no resource info is known.
        """
        pass
    @property
    def scope_spans(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ScopeSpans]:
        """A list of ScopeSpans that originate from a resource."""
        pass
    @property
    def instrumentation_library_spans(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___InstrumentationLibrarySpans]:
        """A list of InstrumentationLibrarySpans that originate from a resource.
        This field is deprecated and will be removed after grace period expires on June 15, 2022.

        During the grace period the following rules SHOULD be followed:

        For Binary Protobufs
        ====================
        Binary Protobuf senders SHOULD NOT set instrumentation_library_spans. Instead
        scope_spans SHOULD be set.

        Binary Protobuf receivers SHOULD check if instrumentation_library_spans is set
        and scope_spans is not set then the value in instrumentation_library_spans
        SHOULD be used instead by converting InstrumentationLibrarySpans into ScopeSpans.
        If scope_spans is set then instrumentation_library_spans SHOULD be ignored.

        For JSON
        ========
        JSON senders that set instrumentation_library_spans field MAY also set
        scope_spans to carry the same spans, essentially double-publishing the same data.
        Such double-publishing MAY be controlled by a user-settable option.
        If double-publishing is not used then the senders SHOULD set scope_spans and
        SHOULD NOT set instrumentation_library_spans.

        JSON receivers SHOULD check if instrumentation_library_spans is set and
        scope_spans is not set then the value in instrumentation_library_spans
        SHOULD be used instead by converting InstrumentationLibrarySpans into ScopeSpans.
        If scope_spans is set then instrumentation_library_spans field SHOULD be ignored.
        """
        pass
    schema_url: typing.Text = ...
    """This schema_url applies to the data in the "resource" field. It does not apply
    to the data in the "scope_spans" field which have their own schema_url field.
    """

    def __init__(self,
        *,
        resource : typing.Optional[opentelemetry.proto.resource.v1.resource_pb2.Resource] = ...,
        scope_spans : typing.Optional[typing.Iterable[global___ScopeSpans]] = ...,
        instrumentation_library_spans : typing.Optional[typing.Iterable[global___InstrumentationLibrarySpans]] = ...,
        schema_url : typing.Text = ...,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["resource",b"resource"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["instrumentation_library_spans",b"instrumentation_library_spans","resource",b"resource","schema_url",b"schema_url","scope_spans",b"scope_spans"]) -> None: ...
global___ResourceSpans = ResourceSpans

class ScopeSpans(google.protobuf.message.Message):
    """A collection of Spans produced by an InstrumentationScope."""
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    SCOPE_FIELD_NUMBER: builtins.int
    SPANS_FIELD_NUMBER: builtins.int
    SCHEMA_URL_FIELD_NUMBER: builtins.int
    @property
    def scope(self) -> opentelemetry.proto.common.v1.common_pb2.InstrumentationScope:
        """The instrumentation scope information for the spans in this message.
        Semantically when InstrumentationScope isn't set, it is equivalent with
        an empty instrumentation scope name (unknown).
        """
        pass
    @property
    def spans(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Span]:
        """A list of Spans that originate from an instrumentation scope."""
        pass
    schema_url: typing.Text = ...
    """This schema_url applies to all spans and span events in the "spans" field."""

    def __init__(self,
        *,
        scope : typing.Optional[opentelemetry.proto.common.v1.common_pb2.InstrumentationScope] = ...,
        spans : typing.Optional[typing.Iterable[global___Span]] = ...,
        schema_url : typing.Text = ...,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["scope",b"scope"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["schema_url",b"schema_url","scope",b"scope","spans",b"spans"]) -> None: ...
global___ScopeSpans = ScopeSpans

class InstrumentationLibrarySpans(google.protobuf.message.Message):
    """A collection of Spans produced by an InstrumentationLibrary.
    InstrumentationLibrarySpans is wire-compatible with ScopeSpans for binary
    Protobuf format.
    This message is deprecated and will be removed on June 15, 2022.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    INSTRUMENTATION_LIBRARY_FIELD_NUMBER: builtins.int
    SPANS_FIELD_NUMBER: builtins.int
    SCHEMA_URL_FIELD_NUMBER: builtins.int
    @property
    def instrumentation_library(self) -> opentelemetry.proto.common.v1.common_pb2.InstrumentationLibrary:
        """The instrumentation library information for the spans in this message.
        Semantically when InstrumentationLibrary isn't set, it is equivalent with
        an empty instrumentation library name (unknown).
        """
        pass
    @property
    def spans(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Span]:
        """A list of Spans that originate from an instrumentation library."""
        pass
    schema_url: typing.Text = ...
    """This schema_url applies to all spans and span events in the "spans" field."""

    def __init__(self,
        *,
        instrumentation_library : typing.Optional[opentelemetry.proto.common.v1.common_pb2.InstrumentationLibrary] = ...,
        spans : typing.Optional[typing.Iterable[global___Span]] = ...,
        schema_url : typing.Text = ...,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["instrumentation_library",b"instrumentation_library"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["instrumentation_library",b"instrumentation_library","schema_url",b"schema_url","spans",b"spans"]) -> None: ...
global___InstrumentationLibrarySpans = InstrumentationLibrarySpans

class Span(google.protobuf.message.Message):
    """Span represents a single operation within a trace. Spans can be
    nested to form a trace tree. Spans may also be linked to other spans
    from the same or different trace and form graphs. Often, a trace
    contains a root span that describes the end-to-end latency, and one
    or more subspans for its sub-operations. A trace can also contain
    multiple root spans, or none at all. Spans do not need to be
    contiguous - there may be gaps or overlaps between spans in a trace.

    The next available field id is 17.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    class SpanKind(_SpanKind, metaclass=_SpanKindEnumTypeWrapper):
        """SpanKind is the type of span. Can be used to specify additional relationships between spans
        in addition to a parent/child relationship.
        """
        pass
    class _SpanKind:
        V = typing.NewType('V', builtins.int)
    class _SpanKindEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_SpanKind.V], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor = ...
        SPAN_KIND_UNSPECIFIED = Span.SpanKind.V(0)
        """Unspecified. Do NOT use as default.
        Implementations MAY assume SpanKind to be INTERNAL when receiving UNSPECIFIED.
        """

        SPAN_KIND_INTERNAL = Span.SpanKind.V(1)
        """Indicates that the span represents an internal operation within an application,
        as opposed to an operation happening at the boundaries. Default value.
        """

        SPAN_KIND_SERVER = Span.SpanKind.V(2)
        """Indicates that the span covers server-side handling of an RPC or other
        remote network request.
        """

        SPAN_KIND_CLIENT = Span.SpanKind.V(3)
        """Indicates that the span describes a request to some remote service."""

        SPAN_KIND_PRODUCER = Span.SpanKind.V(4)
        """Indicates that the span describes a producer sending a message to a broker.
        Unlike CLIENT and SERVER, there is often no direct critical path latency relationship
        between producer and consumer spans. A PRODUCER span ends when the message was accepted
        by the broker while the logical processing of the message might span a much longer time.
        """

        SPAN_KIND_CONSUMER = Span.SpanKind.V(5)
        """Indicates that the span describes consumer receiving a message from a broker.
        Like the PRODUCER kind, there is often no direct critical path latency relationship
        between producer and consumer spans.
        """


    SPAN_KIND_UNSPECIFIED = Span.SpanKind.V(0)
    """Unspecified. Do NOT use as default.
    Implementations MAY assume SpanKind to be INTERNAL when receiving UNSPECIFIED.
    """

    SPAN_KIND_INTERNAL = Span.SpanKind.V(1)
    """Indicates that the span represents an internal operation within an application,
    as opposed to an operation happening at the boundaries. Default value.
    """

    SPAN_KIND_SERVER = Span.SpanKind.V(2)
    """Indicates that the span covers server-side handling of an RPC or other
    remote network request.
    """

    SPAN_KIND_CLIENT = Span.SpanKind.V(3)
    """Indicates that the span describes a request to some remote service."""

    SPAN_KIND_PRODUCER = Span.SpanKind.V(4)
    """Indicates that the span describes a producer sending a message to a broker.
    Unlike CLIENT and SERVER, there is often no direct critical path latency relationship
    between producer and consumer spans. A PRODUCER span ends when the message was accepted
    by the broker while the logical processing of the message might span a much longer time.
    """

    SPAN_KIND_CONSUMER = Span.SpanKind.V(5)
    """Indicates that the span describes consumer receiving a message from a broker.
    Like the PRODUCER kind, there is often no direct critical path latency relationship
    between producer and consumer spans.
    """


    class Event(google.protobuf.message.Message):
        """Event is a time-stamped annotation of the span, consisting of user-supplied
        text description and key-value pairs.
        """
        DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
        TIME_UNIX_NANO_FIELD_NUMBER: builtins.int
        NAME_FIELD_NUMBER: builtins.int
        ATTRIBUTES_FIELD_NUMBER: builtins.int
        DROPPED_ATTRIBUTES_COUNT_FIELD_NUMBER: builtins.int
        time_unix_nano: builtins.int = ...
        """time_unix_nano is the time the event occurred."""

        name: typing.Text = ...
        """name of the event.
        This field is semantically required to be set to non-empty string.
        """

        @property
        def attributes(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[opentelemetry.proto.common.v1.common_pb2.KeyValue]:
            """attributes is a collection of attribute key/value pairs on the event.
            Attribute keys MUST be unique (it is not allowed to have more than one
            attribute with the same key).
            """
            pass
        dropped_attributes_count: builtins.int = ...
        """dropped_attributes_count is the number of dropped attributes. If the value is 0,
        then no attributes were dropped.
        """

        def __init__(self,
            *,
            time_unix_nano : builtins.int = ...,
            name : typing.Text = ...,
            attributes : typing.Optional[typing.Iterable[opentelemetry.proto.common.v1.common_pb2.KeyValue]] = ...,
            dropped_attributes_count : builtins.int = ...,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["attributes",b"attributes","dropped_attributes_count",b"dropped_attributes_count","name",b"name","time_unix_nano",b"time_unix_nano"]) -> None: ...

    class Link(google.protobuf.message.Message):
        """A pointer from the current span to another span in the same trace or in a
        different trace. For example, this can be used in batching operations,
        where a single batch handler processes multiple requests from different
        traces or when the handler receives a request from a different project.
        """
        DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
        TRACE_ID_FIELD_NUMBER: builtins.int
        SPAN_ID_FIELD_NUMBER: builtins.int
        TRACE_STATE_FIELD_NUMBER: builtins.int
        ATTRIBUTES_FIELD_NUMBER: builtins.int
        DROPPED_ATTRIBUTES_COUNT_FIELD_NUMBER: builtins.int
        trace_id: builtins.bytes = ...
        """A unique identifier of a trace that this linked span is part of. The ID is a
        16-byte array.
        """

        span_id: builtins.bytes = ...
        """A unique identifier for the linked span. The ID is an 8-byte array."""

        trace_state: typing.Text = ...
        """The trace_state associated with the link."""

        @property
        def attributes(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[opentelemetry.proto.common.v1.common_pb2.KeyValue]:
            """attributes is a collection of attribute key/value pairs on the link.
            Attribute keys MUST be unique (it is not allowed to have more than one
            attribute with the same key).
            """
            pass
        dropped_attributes_count: builtins.int = ...
        """dropped_attributes_count is the number of dropped attributes. If the value is 0,
        then no attributes were dropped.
        """

        def __init__(self,
            *,
            trace_id : builtins.bytes = ...,
            span_id : builtins.bytes = ...,
            trace_state : typing.Text = ...,
            attributes : typing.Optional[typing.Iterable[opentelemetry.proto.common.v1.common_pb2.KeyValue]] = ...,
            dropped_attributes_count : builtins.int = ...,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["attributes",b"attributes","dropped_attributes_count",b"dropped_attributes_count","span_id",b"span_id","trace_id",b"trace_id","trace_state",b"trace_state"]) -> None: ...

    TRACE_ID_FIELD_NUMBER: builtins.int
    SPAN_ID_FIELD_NUMBER: builtins.int
    TRACE_STATE_FIELD_NUMBER: builtins.int
    PARENT_SPAN_ID_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    KIND_FIELD_NUMBER: builtins.int
    START_TIME_UNIX_NANO_FIELD_NUMBER: builtins.int
    END_TIME_UNIX_NANO_FIELD_NUMBER: builtins.int
    ATTRIBUTES_FIELD_NUMBER: builtins.int
    DROPPED_ATTRIBUTES_COUNT_FIELD_NUMBER: builtins.int
    EVENTS_FIELD_NUMBER: builtins.int
    DROPPED_EVENTS_COUNT_FIELD_NUMBER: builtins.int
    LINKS_FIELD_NUMBER: builtins.int
    DROPPED_LINKS_COUNT_FIELD_NUMBER: builtins.int
    STATUS_FIELD_NUMBER: builtins.int
    trace_id: builtins.bytes = ...
    """A unique identifier for a trace. All spans from the same trace share
    the same `trace_id`. The ID is a 16-byte array. An ID with all zeroes
    is considered invalid.

    This field is semantically required. Receiver should generate new
    random trace_id if empty or invalid trace_id was received.

    This field is required.
    """

    span_id: builtins.bytes = ...
    """A unique identifier for a span within a trace, assigned when the span
    is created. The ID is an 8-byte array. An ID with all zeroes is considered
    invalid.

    This field is semantically required. Receiver should generate new
    random span_id if empty or invalid span_id was received.

    This field is required.
    """

    trace_state: typing.Text = ...
    """trace_state conveys information about request position in multiple distributed tracing graphs.
    It is a trace_state in w3c-trace-context format: https://www.w3.org/TR/trace-context/#tracestate-header
    See also https://github.com/w3c/distributed-tracing for more details about this field.
    """

    parent_span_id: builtins.bytes = ...
    """The `span_id` of this span's parent span. If this is a root span, then this
    field must be empty. The ID is an 8-byte array.
    """

    name: typing.Text = ...
    """A description of the span's operation.

    For example, the name can be a qualified method name or a file name
    and a line number where the operation is called. A best practice is to use
    the same display name at the same call point in an application.
    This makes it easier to correlate spans in different traces.

    This field is semantically required to be set to non-empty string.
    Empty value is equivalent to an unknown span name.

    This field is required.
    """

    kind: global___Span.SpanKind.V = ...
    """Distinguishes between spans generated in a particular context. For example,
    two spans with the same name may be distinguished using `CLIENT` (caller)
    and `SERVER` (callee) to identify queueing latency associated with the span.
    """

    start_time_unix_nano: builtins.int = ...
    """start_time_unix_nano is the start time of the span. On the client side, this is the time
    kept by the local machine where the span execution starts. On the server side, this
    is the time when the server's application handler starts running.
    Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January 1970.

    This field is semantically required and it is expected that end_time >= start_time.
    """

    end_time_unix_nano: builtins.int = ...
    """end_time_unix_nano is the end time of the span. On the client side, this is the time
    kept by the local machine where the span execution ends. On the server side, this
    is the time when the server application handler stops running.
    Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January 1970.

    This field is semantically required and it is expected that end_time >= start_time.
    """

    @property
    def attributes(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[opentelemetry.proto.common.v1.common_pb2.KeyValue]:
        """attributes is a collection of key/value pairs. Note, global attributes
        like server name can be set using the resource API. Examples of attributes:

            "/http/user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"
            "/http/server_latency": 300
            "abc.com/myattribute": true
            "abc.com/score": 10.239

        The OpenTelemetry API specification further restricts the allowed value types:
        https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/common/common.md#attributes
        Attribute keys MUST be unique (it is not allowed to have more than one
        attribute with the same key).
        """
        pass
    dropped_attributes_count: builtins.int = ...
    """dropped_attributes_count is the number of attributes that were discarded. Attributes
    can be discarded because their keys are too long or because there are too many
    attributes. If this value is 0, then no attributes were dropped.
    """

    @property
    def events(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Span.Event]:
        """events is a collection of Event items."""
        pass
    dropped_events_count: builtins.int = ...
    """dropped_events_count is the number of dropped events. If the value is 0, then no
    events were dropped.
    """

    @property
    def links(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Span.Link]:
        """links is a collection of Links, which are references from this span to a span
        in the same or different trace.
        """
        pass
    dropped_links_count: builtins.int = ...
    """dropped_links_count is the number of dropped links after the maximum size was
    enforced. If this value is 0, then no links were dropped.
    """

    @property
    def status(self) -> global___Status:
        """An optional final status for this span. Semantically when Status isn't set, it means
        span's status code is unset, i.e. assume STATUS_CODE_UNSET (code = 0).
        """
        pass
    def __init__(self,
        *,
        trace_id : builtins.bytes = ...,
        span_id : builtins.bytes = ...,
        trace_state : typing.Text = ...,
        parent_span_id : builtins.bytes = ...,
        name : typing.Text = ...,
        kind : global___Span.SpanKind.V = ...,
        start_time_unix_nano : builtins.int = ...,
        end_time_unix_nano : builtins.int = ...,
        attributes : typing.Optional[typing.Iterable[opentelemetry.proto.common.v1.common_pb2.KeyValue]] = ...,
        dropped_attributes_count : builtins.int = ...,
        events : typing.Optional[typing.Iterable[global___Span.Event]] = ...,
        dropped_events_count : builtins.int = ...,
        links : typing.Optional[typing.Iterable[global___Span.Link]] = ...,
        dropped_links_count : builtins.int = ...,
        status : typing.Optional[global___Status] = ...,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["status",b"status"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["attributes",b"attributes","dropped_attributes_count",b"dropped_attributes_count","dropped_events_count",b"dropped_events_count","dropped_links_count",b"dropped_links_count","end_time_unix_nano",b"end_time_unix_nano","events",b"events","kind",b"kind","links",b"links","name",b"name","parent_span_id",b"parent_span_id","span_id",b"span_id","start_time_unix_nano",b"start_time_unix_nano","status",b"status","trace_id",b"trace_id","trace_state",b"trace_state"]) -> None: ...
global___Span = Span

class Status(google.protobuf.message.Message):
    """The Status type defines a logical error model that is suitable for different
    programming environments, including REST APIs and RPC APIs.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    class StatusCode(_StatusCode, metaclass=_StatusCodeEnumTypeWrapper):
        """For the semantics of status codes see
        https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/trace/api.md#set-status
        """
        pass
    class _StatusCode:
        V = typing.NewType('V', builtins.int)
    class _StatusCodeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_StatusCode.V], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor = ...
        STATUS_CODE_UNSET = Status.StatusCode.V(0)
        """The default status."""

        STATUS_CODE_OK = Status.StatusCode.V(1)
        """The Span has been validated by an Application developers or Operator to have
        completed successfully.
        """

        STATUS_CODE_ERROR = Status.StatusCode.V(2)
        """The Span contains an error."""


    STATUS_CODE_UNSET = Status.StatusCode.V(0)
    """The default status."""

    STATUS_CODE_OK = Status.StatusCode.V(1)
    """The Span has been validated by an Application developers or Operator to have
    completed successfully.
    """

    STATUS_CODE_ERROR = Status.StatusCode.V(2)
    """The Span contains an error."""


    MESSAGE_FIELD_NUMBER: builtins.int
    CODE_FIELD_NUMBER: builtins.int
    message: typing.Text = ...
    """A developer-facing human readable error message."""

    code: global___Status.StatusCode.V = ...
    """The status code."""

    def __init__(self,
        *,
        message : typing.Text = ...,
        code : global___Status.StatusCode.V = ...,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["code",b"code","message",b"message"]) -> None: ...
global___Status = Status
