"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
import builtins
import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import typing
import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor = ...

class TraceConfig(google.protobuf.message.Message):
    """Global configuration of the trace service. All fields must be specified, or
    the default (zero) values will be used for each type.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    CONSTANT_SAMPLER_FIELD_NUMBER: builtins.int
    TRACE_ID_RATIO_BASED_FIELD_NUMBER: builtins.int
    RATE_LIMITING_SAMPLER_FIELD_NUMBER: builtins.int
    MAX_NUMBER_OF_ATTRIBUTES_FIELD_NUMBER: builtins.int
    MAX_NUMBER_OF_TIMED_EVENTS_FIELD_NUMBER: builtins.int
    MAX_NUMBER_OF_ATTRIBUTES_PER_TIMED_EVENT_FIELD_NUMBER: builtins.int
    MAX_NUMBER_OF_LINKS_FIELD_NUMBER: builtins.int
    MAX_NUMBER_OF_ATTRIBUTES_PER_LINK_FIELD_NUMBER: builtins.int
    @property
    def constant_sampler(self) -> global___ConstantSampler: ...
    @property
    def trace_id_ratio_based(self) -> global___TraceIdRatioBased: ...
    @property
    def rate_limiting_sampler(self) -> global___RateLimitingSampler: ...
    max_number_of_attributes: builtins.int = ...
    """The global default max number of attributes per span."""

    max_number_of_timed_events: builtins.int = ...
    """The global default max number of annotation events per span."""

    max_number_of_attributes_per_timed_event: builtins.int = ...
    """The global default max number of attributes per timed event."""

    max_number_of_links: builtins.int = ...
    """The global default max number of link entries per span."""

    max_number_of_attributes_per_link: builtins.int = ...
    """The global default max number of attributes per span."""

    def __init__(self,
        *,
        constant_sampler : typing.Optional[global___ConstantSampler] = ...,
        trace_id_ratio_based : typing.Optional[global___TraceIdRatioBased] = ...,
        rate_limiting_sampler : typing.Optional[global___RateLimitingSampler] = ...,
        max_number_of_attributes : builtins.int = ...,
        max_number_of_timed_events : builtins.int = ...,
        max_number_of_attributes_per_timed_event : builtins.int = ...,
        max_number_of_links : builtins.int = ...,
        max_number_of_attributes_per_link : builtins.int = ...,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["constant_sampler",b"constant_sampler","rate_limiting_sampler",b"rate_limiting_sampler","sampler",b"sampler","trace_id_ratio_based",b"trace_id_ratio_based"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["constant_sampler",b"constant_sampler","max_number_of_attributes",b"max_number_of_attributes","max_number_of_attributes_per_link",b"max_number_of_attributes_per_link","max_number_of_attributes_per_timed_event",b"max_number_of_attributes_per_timed_event","max_number_of_links",b"max_number_of_links","max_number_of_timed_events",b"max_number_of_timed_events","rate_limiting_sampler",b"rate_limiting_sampler","sampler",b"sampler","trace_id_ratio_based",b"trace_id_ratio_based"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["sampler",b"sampler"]) -> typing.Optional[typing_extensions.Literal["constant_sampler","trace_id_ratio_based","rate_limiting_sampler"]]: ...
global___TraceConfig = TraceConfig

class ConstantSampler(google.protobuf.message.Message):
    """Sampler that always makes a constant decision on span sampling."""
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    class ConstantDecision(_ConstantDecision, metaclass=_ConstantDecisionEnumTypeWrapper):
        """How spans should be sampled:
        - Always off
        - Always on
        - Always follow the parent Span's decision (off if no parent).
        """
        pass
    class _ConstantDecision:
        V = typing.NewType('V', builtins.int)
    class _ConstantDecisionEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_ConstantDecision.V], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor = ...
        ALWAYS_OFF = ConstantSampler.ConstantDecision.V(0)
        ALWAYS_ON = ConstantSampler.ConstantDecision.V(1)
        ALWAYS_PARENT = ConstantSampler.ConstantDecision.V(2)

    ALWAYS_OFF = ConstantSampler.ConstantDecision.V(0)
    ALWAYS_ON = ConstantSampler.ConstantDecision.V(1)
    ALWAYS_PARENT = ConstantSampler.ConstantDecision.V(2)

    DECISION_FIELD_NUMBER: builtins.int
    decision: global___ConstantSampler.ConstantDecision.V = ...
    def __init__(self,
        *,
        decision : global___ConstantSampler.ConstantDecision.V = ...,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["decision",b"decision"]) -> None: ...
global___ConstantSampler = ConstantSampler

class TraceIdRatioBased(google.protobuf.message.Message):
    """Sampler that tries to uniformly sample traces with a given ratio.
    The ratio of sampling a trace is equal to that of the specified ratio.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    SAMPLINGRATIO_FIELD_NUMBER: builtins.int
    samplingRatio: builtins.float = ...
    """The desired ratio of sampling. Must be within [0.0, 1.0]."""

    def __init__(self,
        *,
        samplingRatio : builtins.float = ...,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["samplingRatio",b"samplingRatio"]) -> None: ...
global___TraceIdRatioBased = TraceIdRatioBased

class RateLimitingSampler(google.protobuf.message.Message):
    """Sampler that tries to sample with a rate per time window."""
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    QPS_FIELD_NUMBER: builtins.int
    qps: builtins.int = ...
    """Rate per second."""

    def __init__(self,
        *,
        qps : builtins.int = ...,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["qps",b"qps"]) -> None: ...
global___RateLimitingSampler = RateLimitingSampler
