"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
import builtins
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import opentelemetry.proto.common.v1.common_pb2
import typing
import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor = ...

class Resource(google.protobuf.message.Message):
    """Resource information."""
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    ATTRIBUTES_FIELD_NUMBER: builtins.int
    DROPPED_ATTRIBUTES_COUNT_FIELD_NUMBER: builtins.int
    @property
    def attributes(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[opentelemetry.proto.common.v1.common_pb2.KeyValue]:
        """Set of attributes that describe the resource.
        Attribute keys MUST be unique (it is not allowed to have more than one
        attribute with the same key).
        """
        pass
    dropped_attributes_count: builtins.int = ...
    """dropped_attributes_count is the number of dropped attributes. If the value is 0, then
    no attributes were dropped.
    """

    def __init__(self,
        *,
        attributes : typing.Optional[typing.Iterable[opentelemetry.proto.common.v1.common_pb2.KeyValue]] = ...,
        dropped_attributes_count : builtins.int = ...,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["attributes",b"attributes","dropped_attributes_count",b"dropped_attributes_count"]) -> None: ...
global___Resource = Resource
