#
# Autogenerated by Thrift Compiler (0.10.0)
#
# DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
#
#  options string: py
#

from thrift.Thrift import TType, TMessageType, TF<PERSON>zenDict, TException, TApplicationException
from thrift.protocol.TProtocol import TProtocolException
import sys
from .ttypes import *
CLIENT_SEND = "cs"
CLIENT_RECV = "cr"
SERVER_SEND = "ss"
SERVER_RECV = "sr"
MESSAGE_SEND = "ms"
MESSAGE_RECV = "mr"
WIRE_SEND = "ws"
WIRE_RECV = "wr"
CLIENT_SEND_FRAGMENT = "csf"
CLIENT_RECV_FRAGMENT = "crf"
SERVER_SEND_FRAGMENT = "ssf"
SERVER_RECV_FRAGMENT = "srf"
LOCAL_COMPONENT = "lc"
CLIENT_ADDR = "ca"
SERVER_ADDR = "sa"
MESSAGE_ADDR = "ma"
