# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: collector.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from opentelemetry.exporter.jaeger.proto.grpc.gen import model_pb2 as model__pb2
from gogoproto import gogo_pb2 as gogoproto_dot_gogo__pb2
from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from protoc_gen_swagger.options import annotations_pb2 as protoc__gen__swagger_dot_options_dot_annotations__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='collector.proto',
  package='jaeger.api_v2',
  syntax='proto3',
  serialized_options=_b('\n\027io.jaegertracing.api_v2Z\006api_v2\310\342\036\001\320\342\036\001\340\342\036\001\300\343\036\001\222AB\022\0052\0031.0*\002\001\002r5\n\nJaeger API\022\'https://github.com/jaegertracing/jaeger'),
  serialized_pb=_b('\n\x0f\x63ollector.proto\x12\rjaeger.api_v2\x1a\x0bmodel.proto\x1a\x14gogoproto/gogo.proto\x1a\x1cgoogle/api/annotations.proto\x1a,protoc-gen-swagger/options/annotations.proto\"=\n\x10PostSpansRequest\x12)\n\x05\x62\x61tch\x18\x01 \x01(\x0b\x32\x14.jaeger.api_v2.BatchB\x04\xc8\xde\x1f\x00\"\x13\n\x11PostSpansResponse2|\n\x10\x43ollectorService\x12h\n\tPostSpans\x12\x1f.jaeger.api_v2.PostSpansRequest\x1a .jaeger.api_v2.PostSpansResponse\"\x18\x82\xd3\xe4\x93\x02\x12\"\r/api/v2/spans:\x01*Bv\n\x17io.jaegertracing.api_v2Z\x06\x61pi_v2\xc8\xe2\x1e\x01\xd0\xe2\x1e\x01\xe0\xe2\x1e\x01\xc0\xe3\x1e\x01\x92\x41\x42\x12\x05\x32\x03\x31.0*\x02\x01\x02r5\n\nJaeger API\x12\'https://github.com/jaegertracing/jaegerb\x06proto3')
  ,
  dependencies=[model__pb2.DESCRIPTOR,gogoproto_dot_gogo__pb2.DESCRIPTOR,google_dot_api_dot_annotations__pb2.DESCRIPTOR,protoc__gen__swagger_dot_options_dot_annotations__pb2.DESCRIPTOR,])




_POSTSPANSREQUEST = _descriptor.Descriptor(
  name='PostSpansRequest',
  full_name='jaeger.api_v2.PostSpansRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch', full_name='jaeger.api_v2.PostSpansRequest.batch', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\310\336\037\000'), file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=145,
  serialized_end=206,
)


_POSTSPANSRESPONSE = _descriptor.Descriptor(
  name='PostSpansResponse',
  full_name='jaeger.api_v2.PostSpansResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=208,
  serialized_end=227,
)

_POSTSPANSREQUEST.fields_by_name['batch'].message_type = model__pb2._BATCH
DESCRIPTOR.message_types_by_name['PostSpansRequest'] = _POSTSPANSREQUEST
DESCRIPTOR.message_types_by_name['PostSpansResponse'] = _POSTSPANSRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

PostSpansRequest = _reflection.GeneratedProtocolMessageType('PostSpansRequest', (_message.Message,), {
  'DESCRIPTOR' : _POSTSPANSREQUEST,
  '__module__' : 'collector_pb2'
  # @@protoc_insertion_point(class_scope:jaeger.api_v2.PostSpansRequest)
  })
_sym_db.RegisterMessage(PostSpansRequest)

PostSpansResponse = _reflection.GeneratedProtocolMessageType('PostSpansResponse', (_message.Message,), {
  'DESCRIPTOR' : _POSTSPANSRESPONSE,
  '__module__' : 'collector_pb2'
  # @@protoc_insertion_point(class_scope:jaeger.api_v2.PostSpansResponse)
  })
_sym_db.RegisterMessage(PostSpansResponse)


DESCRIPTOR._options = None
_POSTSPANSREQUEST.fields_by_name['batch']._options = None

_COLLECTORSERVICE = _descriptor.ServiceDescriptor(
  name='CollectorService',
  full_name='jaeger.api_v2.CollectorService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=229,
  serialized_end=353,
  methods=[
  _descriptor.MethodDescriptor(
    name='PostSpans',
    full_name='jaeger.api_v2.CollectorService.PostSpans',
    index=0,
    containing_service=None,
    input_type=_POSTSPANSREQUEST,
    output_type=_POSTSPANSRESPONSE,
    serialized_options=_b('\202\323\344\223\002\022\"\r/api/v2/spans:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_COLLECTORSERVICE)

DESCRIPTOR.services_by_name['CollectorService'] = _COLLECTORSERVICE

# @@protoc_insertion_point(module_scope)
