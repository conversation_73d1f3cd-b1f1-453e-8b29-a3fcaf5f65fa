# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: protoc-gen-swagger/options/openapiv2.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='protoc-gen-swagger/options/openapiv2.proto',
  package='grpc.gateway.protoc_gen_swagger.options',
  syntax='proto3',
  serialized_options=_b('ZAgithub.com/grpc-ecosystem/grpc-gateway/protoc-gen-swagger/options'),
  serialized_pb=_b('\n*protoc-gen-swagger/options/openapiv2.proto\x12\'grpc.gateway.protoc_gen_swagger.options\x1a\x19google/protobuf/any.proto\x1a\x1cgoogle/protobuf/struct.proto\"\xa0\x07\n\x07Swagger\x12\x0f\n\x07swagger\x18\x01 \x01(\t\x12;\n\x04info\x18\x02 \x01(\x0b\x32-.grpc.gateway.protoc_gen_swagger.options.Info\x12\x0c\n\x04host\x18\x03 \x01(\t\x12\x11\n\tbase_path\x18\x04 \x01(\t\x12O\n\x07schemes\x18\x05 \x03(\x0e\x32>.grpc.gateway.protoc_gen_swagger.options.Swagger.SwaggerScheme\x12\x10\n\x08\x63onsumes\x18\x06 \x03(\t\x12\x10\n\x08produces\x18\x07 \x03(\t\x12R\n\tresponses\x18\n \x03(\x0b\x32?.grpc.gateway.protoc_gen_swagger.options.Swagger.ResponsesEntry\x12Z\n\x14security_definitions\x18\x0b \x01(\x0b\x32<.grpc.gateway.protoc_gen_swagger.options.SecurityDefinitions\x12N\n\x08security\x18\x0c \x03(\x0b\x32<.grpc.gateway.protoc_gen_swagger.options.SecurityRequirement\x12U\n\rexternal_docs\x18\x0e \x01(\x0b\x32>.grpc.gateway.protoc_gen_swagger.options.ExternalDocumentation\x12T\n\nextensions\x18\x0f \x03(\x0b\<EMAIL>.protoc_gen_swagger.options.Swagger.ExtensionsEntry\x1a\x63\n\x0eResponsesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12@\n\x05value\x18\x02 \x01(\x0b\x32\x31.grpc.gateway.protoc_gen_swagger.options.Response:\x02\x38\x01\x1aI\n\x0f\x45xtensionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\"B\n\rSwaggerScheme\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x08\n\x04HTTP\x10\x01\x12\t\n\x05HTTPS\x10\x02\x12\x06\n\x02WS\x10\x03\x12\x07\n\x03WSS\x10\x04J\x04\x08\x08\x10\tJ\x04\x08\t\x10\nJ\x04\x08\r\x10\x0e\"\xa9\x05\n\tOperation\x12\x0c\n\x04tags\x18\x01 \x03(\t\x12\x0f\n\x07summary\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12U\n\rexternal_docs\x18\x04 \x01(\x0b\x32>.grpc.gateway.protoc_gen_swagger.options.ExternalDocumentation\x12\x14\n\x0coperation_id\x18\x05 \x01(\t\x12\x10\n\x08\x63onsumes\x18\x06 \x03(\t\x12\x10\n\x08produces\x18\x07 \x03(\t\x12T\n\tresponses\x18\t \x03(\x0b\x32\x41.grpc.gateway.protoc_gen_swagger.options.Operation.ResponsesEntry\x12\x0f\n\x07schemes\x18\n \x03(\t\x12\x12\n\ndeprecated\x18\x0b \x01(\x08\x12N\n\x08security\x18\x0c \x03(\x0b\x32<.grpc.gateway.protoc_gen_swagger.options.SecurityRequirement\x12V\n\nextensions\x18\r \x03(\x0b\x32\x42.grpc.gateway.protoc_gen_swagger.options.Operation.ExtensionsEntry\x1a\x63\n\x0eResponsesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12@\n\x05value\x18\x02 \x01(\x0b\x32\x31.grpc.gateway.protoc_gen_swagger.options.Response:\x02\x38\x01\x1aI\n\x0f\x45xtensionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01J\x04\x08\x08\x10\t\"\x8e\x02\n\x08Response\x12\x13\n\x0b\x64\x65scription\x18\x01 \x01(\t\x12?\n\x06schema\x18\x02 \x01(\x0b\x32/.grpc.gateway.protoc_gen_swagger.options.Schema\x12U\n\nextensions\x18\x05 \x03(\x0b\x32\x41.grpc.gateway.protoc_gen_swagger.options.Response.ExtensionsEntry\x1aI\n\x0f\x45xtensionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01J\x04\x08\x03\x10\x04J\x04\x08\x04\x10\x05\"\xf9\x02\n\x04Info\x12\r\n\x05title\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x18\n\x10terms_of_service\x18\x03 \x01(\t\x12\x41\n\x07\x63ontact\x18\x04 \x01(\x0b\x32\x30.grpc.gateway.protoc_gen_swagger.options.Contact\x12\x41\n\x07license\x18\x05 \x01(\x0b\x32\x30.grpc.gateway.protoc_gen_swagger.options.License\x12\x0f\n\x07version\x18\x06 \x01(\t\x12Q\n\nextensions\x18\x07 \x03(\x0b\x32=.grpc.gateway.protoc_gen_swagger.options.Info.ExtensionsEntry\x1aI\n\x0f\x45xtensionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\"3\n\x07\x43ontact\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\x12\r\n\x05\x65mail\x18\x03 \x01(\t\"$\n\x07License\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\"9\n\x15\x45xternalDocumentation\x12\x13\n\x0b\x64\x65scription\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\"\x80\x02\n\x06Schema\x12H\n\x0bjson_schema\x18\x01 \x01(\x0b\x32\x33.grpc.gateway.protoc_gen_swagger.options.JSONSchema\x12\x15\n\rdiscriminator\x18\x02 \x01(\t\x12\x11\n\tread_only\x18\x03 \x01(\x08\x12U\n\rexternal_docs\x18\x05 \x01(\x0b\x32>.grpc.gateway.protoc_gen_swagger.options.ExternalDocumentation\x12%\n\x07\x65xample\x18\x06 \x01(\x0b\x32\x14.google.protobuf.AnyJ\x04\x08\x04\x10\x05\"\xba\x05\n\nJSONSchema\x12\x0b\n\x03ref\x18\x03 \x01(\t\x12\r\n\x05title\x18\x05 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\x07 \x01(\t\x12\x11\n\tread_only\x18\x08 \x01(\x08\x12\x13\n\x0bmultiple_of\x18\n \x01(\x01\x12\x0f\n\x07maximum\x18\x0b \x01(\x01\x12\x19\n\x11\x65xclusive_maximum\x18\x0c \x01(\x08\x12\x0f\n\x07minimum\x18\r \x01(\x01\x12\x19\n\x11\x65xclusive_minimum\x18\x0e \x01(\x08\x12\x12\n\nmax_length\x18\x0f \x01(\x04\x12\x12\n\nmin_length\x18\x10 \x01(\x04\x12\x0f\n\x07pattern\x18\x11 \x01(\t\x12\x11\n\tmax_items\x18\x14 \x01(\x04\x12\x11\n\tmin_items\x18\x15 \x01(\x04\x12\x14\n\x0cunique_items\x18\x16 \x01(\x08\x12\x16\n\x0emax_properties\x18\x18 \x01(\x04\x12\x16\n\x0emin_properties\x18\x19 \x01(\x04\x12\x10\n\x08required\x18\x1a \x03(\t\x12\r\n\x05\x61rray\x18\" \x03(\t\x12W\n\x04type\x18# \x03(\x0e\x32I.grpc.gateway.protoc_gen_swagger.options.JSONSchema.JSONSchemaSimpleTypes\"w\n\x15JSONSchemaSimpleTypes\x12\x0b\n\x07UNKNOWN\x10\x00\x12\t\n\x05\x41RRAY\x10\x01\x12\x0b\n\x07\x42OOLEAN\x10\x02\x12\x0b\n\x07INTEGER\x10\x03\x12\x08\n\x04NULL\x10\x04\x12\n\n\x06NUMBER\x10\x05\x12\n\n\x06OBJECT\x10\x06\x12\n\n\x06STRING\x10\x07J\x04\x08\x01\x10\x02J\x04\x08\x02\x10\x03J\x04\x08\x04\x10\x05J\x04\x08\t\x10\nJ\x04\x08\x12\x10\x13J\x04\x08\x13\x10\x14J\x04\x08\x17\x10\x18J\x04\x08\x1b\x10\x1cJ\x04\x08\x1c\x10\x1dJ\x04\x08\x1d\x10\x1eJ\x04\x08\x1e\x10\"J\x04\x08$\x10*J\x04\x08*\x10+J\x04\x08+\x10.\"w\n\x03Tag\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12U\n\rexternal_docs\x18\x03 \x01(\x0b\x32>.grpc.gateway.protoc_gen_swagger.options.ExternalDocumentationJ\x04\x08\x01\x10\x02\"\xdd\x01\n\x13SecurityDefinitions\x12\\\n\x08security\x18\x01 \x03(\x0b\x32J.grpc.gateway.protoc_gen_swagger.options.SecurityDefinitions.SecurityEntry\x1ah\n\rSecurityEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x46\n\x05value\x18\x02 \x01(\x0b\x32\x37.grpc.gateway.protoc_gen_swagger.options.SecurityScheme:\x02\x38\x01\"\x96\x06\n\x0eSecurityScheme\x12J\n\x04type\x18\x01 \x01(\x0e\x32<.grpc.gateway.protoc_gen_swagger.options.SecurityScheme.Type\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x46\n\x02in\x18\x04 \x01(\x0e\x32:.grpc.gateway.protoc_gen_swagger.options.SecurityScheme.In\x12J\n\x04\x66low\x18\x05 \x01(\x0e\x32<.grpc.gateway.protoc_gen_swagger.options.SecurityScheme.Flow\x12\x19\n\x11\x61uthorization_url\x18\x06 \x01(\t\x12\x11\n\ttoken_url\x18\x07 \x01(\t\x12?\n\x06scopes\x18\x08 \x01(\x0b\x32/.grpc.gateway.protoc_gen_swagger.options.Scopes\x12[\n\nextensions\x18\t \x03(\x0b\x32G.grpc.gateway.protoc_gen_swagger.options.SecurityScheme.ExtensionsEntry\x1aI\n\x0f\x45xtensionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\"K\n\x04Type\x12\x10\n\x0cTYPE_INVALID\x10\x00\x12\x0e\n\nTYPE_BASIC\x10\x01\x12\x10\n\x0cTYPE_API_KEY\x10\x02\x12\x0f\n\x0bTYPE_OAUTH2\x10\x03\"1\n\x02In\x12\x0e\n\nIN_INVALID\x10\x00\x12\x0c\n\x08IN_QUERY\x10\x01\x12\r\n\tIN_HEADER\x10\x02\"j\n\x04\x46low\x12\x10\n\x0c\x46LOW_INVALID\x10\x00\x12\x11\n\rFLOW_IMPLICIT\x10\x01\x12\x11\n\rFLOW_PASSWORD\x10\x02\x12\x14\n\x10\x46LOW_APPLICATION\x10\x03\x12\x14\n\x10\x46LOW_ACCESS_CODE\x10\x04\"\xc9\x02\n\x13SecurityRequirement\x12s\n\x14security_requirement\x18\x01 \x03(\x0b\x32U.grpc.gateway.protoc_gen_swagger.options.SecurityRequirement.SecurityRequirementEntry\x1a)\n\x18SecurityRequirementValue\x12\r\n\x05scope\x18\x01 \x03(\t\x1a\x91\x01\n\x18SecurityRequirementEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x64\n\x05value\x18\x02 \x01(\x0b\x32U.grpc.gateway.protoc_gen_swagger.options.SecurityRequirement.SecurityRequirementValue:\x02\x38\x01\"\x81\x01\n\x06Scopes\x12I\n\x05scope\x18\x01 \x03(\x0b\x32:.grpc.gateway.protoc_gen_swagger.options.Scopes.ScopeEntry\x1a,\n\nScopeEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x42\x43ZAgithub.com/grpc-ecosystem/grpc-gateway/protoc-gen-swagger/optionsb\x06proto3')
  ,
  dependencies=[google_dot_protobuf_dot_any__pb2.DESCRIPTOR,google_dot_protobuf_dot_struct__pb2.DESCRIPTOR,])



_SWAGGER_SWAGGERSCHEME = _descriptor.EnumDescriptor(
  name='SwaggerScheme',
  full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.SwaggerScheme',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='HTTP', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='HTTPS', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WS', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WSS', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=989,
  serialized_end=1055,
)
_sym_db.RegisterEnumDescriptor(_SWAGGER_SWAGGERSCHEME)

_JSONSCHEMA_JSONSCHEMASIMPLETYPES = _descriptor.EnumDescriptor(
  name='JSONSchemaSimpleTypes',
  full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.JSONSchemaSimpleTypes',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ARRAY', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BOOLEAN', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INTEGER', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NULL', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NUMBER', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OBJECT', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='STRING', index=7, number=7,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3317,
  serialized_end=3436,
)
_sym_db.RegisterEnumDescriptor(_JSONSCHEMA_JSONSCHEMASIMPLETYPES)

_SECURITYSCHEME_TYPE = _descriptor.EnumDescriptor(
  name='Type',
  full_name='grpc.gateway.protoc_gen_swagger.options.SecurityScheme.Type',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TYPE_INVALID', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TYPE_BASIC', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TYPE_API_KEY', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TYPE_OAUTH2', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4424,
  serialized_end=4499,
)
_sym_db.RegisterEnumDescriptor(_SECURITYSCHEME_TYPE)

_SECURITYSCHEME_IN = _descriptor.EnumDescriptor(
  name='In',
  full_name='grpc.gateway.protoc_gen_swagger.options.SecurityScheme.In',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='IN_INVALID', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='IN_QUERY', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='IN_HEADER', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4501,
  serialized_end=4550,
)
_sym_db.RegisterEnumDescriptor(_SECURITYSCHEME_IN)

_SECURITYSCHEME_FLOW = _descriptor.EnumDescriptor(
  name='Flow',
  full_name='grpc.gateway.protoc_gen_swagger.options.SecurityScheme.Flow',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='FLOW_INVALID', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FLOW_IMPLICIT', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FLOW_PASSWORD', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FLOW_APPLICATION', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FLOW_ACCESS_CODE', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4552,
  serialized_end=4658,
)
_sym_db.RegisterEnumDescriptor(_SECURITYSCHEME_FLOW)


_SWAGGER_RESPONSESENTRY = _descriptor.Descriptor(
  name='ResponsesEntry',
  full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.ResponsesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.ResponsesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.ResponsesEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=813,
  serialized_end=912,
)

_SWAGGER_EXTENSIONSENTRY = _descriptor.Descriptor(
  name='ExtensionsEntry',
  full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.ExtensionsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.ExtensionsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.ExtensionsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=914,
  serialized_end=987,
)

_SWAGGER = _descriptor.Descriptor(
  name='Swagger',
  full_name='grpc.gateway.protoc_gen_swagger.options.Swagger',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='swagger', full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.swagger', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='info', full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.info', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host', full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.host', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='base_path', full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.base_path', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schemes', full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.schemes', index=4,
      number=5, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='consumes', full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.consumes', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='produces', full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.produces', index=6,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='responses', full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.responses', index=7,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='security_definitions', full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.security_definitions', index=8,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='security', full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.security', index=9,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='external_docs', full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.external_docs', index=10,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extensions', full_name='grpc.gateway.protoc_gen_swagger.options.Swagger.extensions', index=11,
      number=15, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_SWAGGER_RESPONSESENTRY, _SWAGGER_EXTENSIONSENTRY, ],
  enum_types=[
    _SWAGGER_SWAGGERSCHEME,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=145,
  serialized_end=1073,
)


_OPERATION_RESPONSESENTRY = _descriptor.Descriptor(
  name='ResponsesEntry',
  full_name='grpc.gateway.protoc_gen_swagger.options.Operation.ResponsesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='grpc.gateway.protoc_gen_swagger.options.Operation.ResponsesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='grpc.gateway.protoc_gen_swagger.options.Operation.ResponsesEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=813,
  serialized_end=912,
)

_OPERATION_EXTENSIONSENTRY = _descriptor.Descriptor(
  name='ExtensionsEntry',
  full_name='grpc.gateway.protoc_gen_swagger.options.Operation.ExtensionsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='grpc.gateway.protoc_gen_swagger.options.Operation.ExtensionsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='grpc.gateway.protoc_gen_swagger.options.Operation.ExtensionsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=914,
  serialized_end=987,
)

_OPERATION = _descriptor.Descriptor(
  name='Operation',
  full_name='grpc.gateway.protoc_gen_swagger.options.Operation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tags', full_name='grpc.gateway.protoc_gen_swagger.options.Operation.tags', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='summary', full_name='grpc.gateway.protoc_gen_swagger.options.Operation.summary', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='grpc.gateway.protoc_gen_swagger.options.Operation.description', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='external_docs', full_name='grpc.gateway.protoc_gen_swagger.options.Operation.external_docs', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operation_id', full_name='grpc.gateway.protoc_gen_swagger.options.Operation.operation_id', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='consumes', full_name='grpc.gateway.protoc_gen_swagger.options.Operation.consumes', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='produces', full_name='grpc.gateway.protoc_gen_swagger.options.Operation.produces', index=6,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='responses', full_name='grpc.gateway.protoc_gen_swagger.options.Operation.responses', index=7,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schemes', full_name='grpc.gateway.protoc_gen_swagger.options.Operation.schemes', index=8,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deprecated', full_name='grpc.gateway.protoc_gen_swagger.options.Operation.deprecated', index=9,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='security', full_name='grpc.gateway.protoc_gen_swagger.options.Operation.security', index=10,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extensions', full_name='grpc.gateway.protoc_gen_swagger.options.Operation.extensions', index=11,
      number=13, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_OPERATION_RESPONSESENTRY, _OPERATION_EXTENSIONSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1076,
  serialized_end=1757,
)


_RESPONSE_EXTENSIONSENTRY = _descriptor.Descriptor(
  name='ExtensionsEntry',
  full_name='grpc.gateway.protoc_gen_swagger.options.Response.ExtensionsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='grpc.gateway.protoc_gen_swagger.options.Response.ExtensionsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='grpc.gateway.protoc_gen_swagger.options.Response.ExtensionsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=914,
  serialized_end=987,
)

_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='grpc.gateway.protoc_gen_swagger.options.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='description', full_name='grpc.gateway.protoc_gen_swagger.options.Response.description', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema', full_name='grpc.gateway.protoc_gen_swagger.options.Response.schema', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extensions', full_name='grpc.gateway.protoc_gen_swagger.options.Response.extensions', index=2,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_RESPONSE_EXTENSIONSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1760,
  serialized_end=2030,
)


_INFO_EXTENSIONSENTRY = _descriptor.Descriptor(
  name='ExtensionsEntry',
  full_name='grpc.gateway.protoc_gen_swagger.options.Info.ExtensionsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='grpc.gateway.protoc_gen_swagger.options.Info.ExtensionsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='grpc.gateway.protoc_gen_swagger.options.Info.ExtensionsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=914,
  serialized_end=987,
)

_INFO = _descriptor.Descriptor(
  name='Info',
  full_name='grpc.gateway.protoc_gen_swagger.options.Info',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='title', full_name='grpc.gateway.protoc_gen_swagger.options.Info.title', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='grpc.gateway.protoc_gen_swagger.options.Info.description', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='terms_of_service', full_name='grpc.gateway.protoc_gen_swagger.options.Info.terms_of_service', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contact', full_name='grpc.gateway.protoc_gen_swagger.options.Info.contact', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='license', full_name='grpc.gateway.protoc_gen_swagger.options.Info.license', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='version', full_name='grpc.gateway.protoc_gen_swagger.options.Info.version', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extensions', full_name='grpc.gateway.protoc_gen_swagger.options.Info.extensions', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_INFO_EXTENSIONSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2033,
  serialized_end=2410,
)


_CONTACT = _descriptor.Descriptor(
  name='Contact',
  full_name='grpc.gateway.protoc_gen_swagger.options.Contact',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='grpc.gateway.protoc_gen_swagger.options.Contact.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='grpc.gateway.protoc_gen_swagger.options.Contact.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='email', full_name='grpc.gateway.protoc_gen_swagger.options.Contact.email', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2412,
  serialized_end=2463,
)


_LICENSE = _descriptor.Descriptor(
  name='License',
  full_name='grpc.gateway.protoc_gen_swagger.options.License',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='grpc.gateway.protoc_gen_swagger.options.License.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='grpc.gateway.protoc_gen_swagger.options.License.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2465,
  serialized_end=2501,
)


_EXTERNALDOCUMENTATION = _descriptor.Descriptor(
  name='ExternalDocumentation',
  full_name='grpc.gateway.protoc_gen_swagger.options.ExternalDocumentation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='description', full_name='grpc.gateway.protoc_gen_swagger.options.ExternalDocumentation.description', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='grpc.gateway.protoc_gen_swagger.options.ExternalDocumentation.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2503,
  serialized_end=2560,
)


_SCHEMA = _descriptor.Descriptor(
  name='Schema',
  full_name='grpc.gateway.protoc_gen_swagger.options.Schema',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='json_schema', full_name='grpc.gateway.protoc_gen_swagger.options.Schema.json_schema', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='discriminator', full_name='grpc.gateway.protoc_gen_swagger.options.Schema.discriminator', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='read_only', full_name='grpc.gateway.protoc_gen_swagger.options.Schema.read_only', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='external_docs', full_name='grpc.gateway.protoc_gen_swagger.options.Schema.external_docs', index=3,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='example', full_name='grpc.gateway.protoc_gen_swagger.options.Schema.example', index=4,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2563,
  serialized_end=2819,
)


_JSONSCHEMA = _descriptor.Descriptor(
  name='JSONSchema',
  full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ref', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.ref', index=0,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.title', index=1,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.description', index=2,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.default', index=3,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='read_only', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.read_only', index=4,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='multiple_of', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.multiple_of', index=5,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='maximum', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.maximum', index=6,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exclusive_maximum', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.exclusive_maximum', index=7,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='minimum', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.minimum', index=8,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exclusive_minimum', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.exclusive_minimum', index=9,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_length', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.max_length', index=10,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_length', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.min_length', index=11,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pattern', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.pattern', index=12,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_items', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.max_items', index=13,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_items', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.min_items', index=14,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unique_items', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.unique_items', index=15,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_properties', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.max_properties', index=16,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_properties', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.min_properties', index=17,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='required', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.required', index=18,
      number=26, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='array', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.array', index=19,
      number=34, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='grpc.gateway.protoc_gen_swagger.options.JSONSchema.type', index=20,
      number=35, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _JSONSCHEMA_JSONSCHEMASIMPLETYPES,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2822,
  serialized_end=3520,
)


_TAG = _descriptor.Descriptor(
  name='Tag',
  full_name='grpc.gateway.protoc_gen_swagger.options.Tag',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='description', full_name='grpc.gateway.protoc_gen_swagger.options.Tag.description', index=0,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='external_docs', full_name='grpc.gateway.protoc_gen_swagger.options.Tag.external_docs', index=1,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3522,
  serialized_end=3641,
)


_SECURITYDEFINITIONS_SECURITYENTRY = _descriptor.Descriptor(
  name='SecurityEntry',
  full_name='grpc.gateway.protoc_gen_swagger.options.SecurityDefinitions.SecurityEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityDefinitions.SecurityEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityDefinitions.SecurityEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3761,
  serialized_end=3865,
)

_SECURITYDEFINITIONS = _descriptor.Descriptor(
  name='SecurityDefinitions',
  full_name='grpc.gateway.protoc_gen_swagger.options.SecurityDefinitions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='security', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityDefinitions.security', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_SECURITYDEFINITIONS_SECURITYENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3644,
  serialized_end=3865,
)


_SECURITYSCHEME_EXTENSIONSENTRY = _descriptor.Descriptor(
  name='ExtensionsEntry',
  full_name='grpc.gateway.protoc_gen_swagger.options.SecurityScheme.ExtensionsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityScheme.ExtensionsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityScheme.ExtensionsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=914,
  serialized_end=987,
)

_SECURITYSCHEME = _descriptor.Descriptor(
  name='SecurityScheme',
  full_name='grpc.gateway.protoc_gen_swagger.options.SecurityScheme',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityScheme.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityScheme.description', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityScheme.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='in', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityScheme.in', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='flow', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityScheme.flow', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='authorization_url', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityScheme.authorization_url', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='token_url', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityScheme.token_url', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scopes', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityScheme.scopes', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extensions', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityScheme.extensions', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_SECURITYSCHEME_EXTENSIONSENTRY, ],
  enum_types=[
    _SECURITYSCHEME_TYPE,
    _SECURITYSCHEME_IN,
    _SECURITYSCHEME_FLOW,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3868,
  serialized_end=4658,
)


_SECURITYREQUIREMENT_SECURITYREQUIREMENTVALUE = _descriptor.Descriptor(
  name='SecurityRequirementValue',
  full_name='grpc.gateway.protoc_gen_swagger.options.SecurityRequirement.SecurityRequirementValue',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='scope', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityRequirement.SecurityRequirementValue.scope', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4801,
  serialized_end=4842,
)

_SECURITYREQUIREMENT_SECURITYREQUIREMENTENTRY = _descriptor.Descriptor(
  name='SecurityRequirementEntry',
  full_name='grpc.gateway.protoc_gen_swagger.options.SecurityRequirement.SecurityRequirementEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityRequirement.SecurityRequirementEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityRequirement.SecurityRequirementEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4845,
  serialized_end=4990,
)

_SECURITYREQUIREMENT = _descriptor.Descriptor(
  name='SecurityRequirement',
  full_name='grpc.gateway.protoc_gen_swagger.options.SecurityRequirement',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='security_requirement', full_name='grpc.gateway.protoc_gen_swagger.options.SecurityRequirement.security_requirement', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_SECURITYREQUIREMENT_SECURITYREQUIREMENTVALUE, _SECURITYREQUIREMENT_SECURITYREQUIREMENTENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4661,
  serialized_end=4990,
)


_SCOPES_SCOPEENTRY = _descriptor.Descriptor(
  name='ScopeEntry',
  full_name='grpc.gateway.protoc_gen_swagger.options.Scopes.ScopeEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='grpc.gateway.protoc_gen_swagger.options.Scopes.ScopeEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='grpc.gateway.protoc_gen_swagger.options.Scopes.ScopeEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5078,
  serialized_end=5122,
)

_SCOPES = _descriptor.Descriptor(
  name='Scopes',
  full_name='grpc.gateway.protoc_gen_swagger.options.Scopes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='scope', full_name='grpc.gateway.protoc_gen_swagger.options.Scopes.scope', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_SCOPES_SCOPEENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4993,
  serialized_end=5122,
)

_SWAGGER_RESPONSESENTRY.fields_by_name['value'].message_type = _RESPONSE
_SWAGGER_RESPONSESENTRY.containing_type = _SWAGGER
_SWAGGER_EXTENSIONSENTRY.fields_by_name['value'].message_type = google_dot_protobuf_dot_struct__pb2._VALUE
_SWAGGER_EXTENSIONSENTRY.containing_type = _SWAGGER
_SWAGGER.fields_by_name['info'].message_type = _INFO
_SWAGGER.fields_by_name['schemes'].enum_type = _SWAGGER_SWAGGERSCHEME
_SWAGGER.fields_by_name['responses'].message_type = _SWAGGER_RESPONSESENTRY
_SWAGGER.fields_by_name['security_definitions'].message_type = _SECURITYDEFINITIONS
_SWAGGER.fields_by_name['security'].message_type = _SECURITYREQUIREMENT
_SWAGGER.fields_by_name['external_docs'].message_type = _EXTERNALDOCUMENTATION
_SWAGGER.fields_by_name['extensions'].message_type = _SWAGGER_EXTENSIONSENTRY
_SWAGGER_SWAGGERSCHEME.containing_type = _SWAGGER
_OPERATION_RESPONSESENTRY.fields_by_name['value'].message_type = _RESPONSE
_OPERATION_RESPONSESENTRY.containing_type = _OPERATION
_OPERATION_EXTENSIONSENTRY.fields_by_name['value'].message_type = google_dot_protobuf_dot_struct__pb2._VALUE
_OPERATION_EXTENSIONSENTRY.containing_type = _OPERATION
_OPERATION.fields_by_name['external_docs'].message_type = _EXTERNALDOCUMENTATION
_OPERATION.fields_by_name['responses'].message_type = _OPERATION_RESPONSESENTRY
_OPERATION.fields_by_name['security'].message_type = _SECURITYREQUIREMENT
_OPERATION.fields_by_name['extensions'].message_type = _OPERATION_EXTENSIONSENTRY
_RESPONSE_EXTENSIONSENTRY.fields_by_name['value'].message_type = google_dot_protobuf_dot_struct__pb2._VALUE
_RESPONSE_EXTENSIONSENTRY.containing_type = _RESPONSE
_RESPONSE.fields_by_name['schema'].message_type = _SCHEMA
_RESPONSE.fields_by_name['extensions'].message_type = _RESPONSE_EXTENSIONSENTRY
_INFO_EXTENSIONSENTRY.fields_by_name['value'].message_type = google_dot_protobuf_dot_struct__pb2._VALUE
_INFO_EXTENSIONSENTRY.containing_type = _INFO
_INFO.fields_by_name['contact'].message_type = _CONTACT
_INFO.fields_by_name['license'].message_type = _LICENSE
_INFO.fields_by_name['extensions'].message_type = _INFO_EXTENSIONSENTRY
_SCHEMA.fields_by_name['json_schema'].message_type = _JSONSCHEMA
_SCHEMA.fields_by_name['external_docs'].message_type = _EXTERNALDOCUMENTATION
_SCHEMA.fields_by_name['example'].message_type = google_dot_protobuf_dot_any__pb2._ANY
_JSONSCHEMA.fields_by_name['type'].enum_type = _JSONSCHEMA_JSONSCHEMASIMPLETYPES
_JSONSCHEMA_JSONSCHEMASIMPLETYPES.containing_type = _JSONSCHEMA
_TAG.fields_by_name['external_docs'].message_type = _EXTERNALDOCUMENTATION
_SECURITYDEFINITIONS_SECURITYENTRY.fields_by_name['value'].message_type = _SECURITYSCHEME
_SECURITYDEFINITIONS_SECURITYENTRY.containing_type = _SECURITYDEFINITIONS
_SECURITYDEFINITIONS.fields_by_name['security'].message_type = _SECURITYDEFINITIONS_SECURITYENTRY
_SECURITYSCHEME_EXTENSIONSENTRY.fields_by_name['value'].message_type = google_dot_protobuf_dot_struct__pb2._VALUE
_SECURITYSCHEME_EXTENSIONSENTRY.containing_type = _SECURITYSCHEME
_SECURITYSCHEME.fields_by_name['type'].enum_type = _SECURITYSCHEME_TYPE
_SECURITYSCHEME.fields_by_name['in'].enum_type = _SECURITYSCHEME_IN
_SECURITYSCHEME.fields_by_name['flow'].enum_type = _SECURITYSCHEME_FLOW
_SECURITYSCHEME.fields_by_name['scopes'].message_type = _SCOPES
_SECURITYSCHEME.fields_by_name['extensions'].message_type = _SECURITYSCHEME_EXTENSIONSENTRY
_SECURITYSCHEME_TYPE.containing_type = _SECURITYSCHEME
_SECURITYSCHEME_IN.containing_type = _SECURITYSCHEME
_SECURITYSCHEME_FLOW.containing_type = _SECURITYSCHEME
_SECURITYREQUIREMENT_SECURITYREQUIREMENTVALUE.containing_type = _SECURITYREQUIREMENT
_SECURITYREQUIREMENT_SECURITYREQUIREMENTENTRY.fields_by_name['value'].message_type = _SECURITYREQUIREMENT_SECURITYREQUIREMENTVALUE
_SECURITYREQUIREMENT_SECURITYREQUIREMENTENTRY.containing_type = _SECURITYREQUIREMENT
_SECURITYREQUIREMENT.fields_by_name['security_requirement'].message_type = _SECURITYREQUIREMENT_SECURITYREQUIREMENTENTRY
_SCOPES_SCOPEENTRY.containing_type = _SCOPES
_SCOPES.fields_by_name['scope'].message_type = _SCOPES_SCOPEENTRY
DESCRIPTOR.message_types_by_name['Swagger'] = _SWAGGER
DESCRIPTOR.message_types_by_name['Operation'] = _OPERATION
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
DESCRIPTOR.message_types_by_name['Info'] = _INFO
DESCRIPTOR.message_types_by_name['Contact'] = _CONTACT
DESCRIPTOR.message_types_by_name['License'] = _LICENSE
DESCRIPTOR.message_types_by_name['ExternalDocumentation'] = _EXTERNALDOCUMENTATION
DESCRIPTOR.message_types_by_name['Schema'] = _SCHEMA
DESCRIPTOR.message_types_by_name['JSONSchema'] = _JSONSCHEMA
DESCRIPTOR.message_types_by_name['Tag'] = _TAG
DESCRIPTOR.message_types_by_name['SecurityDefinitions'] = _SECURITYDEFINITIONS
DESCRIPTOR.message_types_by_name['SecurityScheme'] = _SECURITYSCHEME
DESCRIPTOR.message_types_by_name['SecurityRequirement'] = _SECURITYREQUIREMENT
DESCRIPTOR.message_types_by_name['Scopes'] = _SCOPES
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Swagger = _reflection.GeneratedProtocolMessageType('Swagger', (_message.Message,), {

  'ResponsesEntry' : _reflection.GeneratedProtocolMessageType('ResponsesEntry', (_message.Message,), {
    'DESCRIPTOR' : _SWAGGER_RESPONSESENTRY,
    '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
    # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.Swagger.ResponsesEntry)
    })
  ,

  'ExtensionsEntry' : _reflection.GeneratedProtocolMessageType('ExtensionsEntry', (_message.Message,), {
    'DESCRIPTOR' : _SWAGGER_EXTENSIONSENTRY,
    '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
    # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.Swagger.ExtensionsEntry)
    })
  ,
  'DESCRIPTOR' : _SWAGGER,
  '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
  # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.Swagger)
  })
_sym_db.RegisterMessage(Swagger)
_sym_db.RegisterMessage(Swagger.ResponsesEntry)
_sym_db.RegisterMessage(Swagger.ExtensionsEntry)

Operation = _reflection.GeneratedProtocolMessageType('Operation', (_message.Message,), {

  'ResponsesEntry' : _reflection.GeneratedProtocolMessageType('ResponsesEntry', (_message.Message,), {
    'DESCRIPTOR' : _OPERATION_RESPONSESENTRY,
    '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
    # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.Operation.ResponsesEntry)
    })
  ,

  'ExtensionsEntry' : _reflection.GeneratedProtocolMessageType('ExtensionsEntry', (_message.Message,), {
    'DESCRIPTOR' : _OPERATION_EXTENSIONSENTRY,
    '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
    # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.Operation.ExtensionsEntry)
    })
  ,
  'DESCRIPTOR' : _OPERATION,
  '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
  # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.Operation)
  })
_sym_db.RegisterMessage(Operation)
_sym_db.RegisterMessage(Operation.ResponsesEntry)
_sym_db.RegisterMessage(Operation.ExtensionsEntry)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {

  'ExtensionsEntry' : _reflection.GeneratedProtocolMessageType('ExtensionsEntry', (_message.Message,), {
    'DESCRIPTOR' : _RESPONSE_EXTENSIONSENTRY,
    '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
    # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.Response.ExtensionsEntry)
    })
  ,
  'DESCRIPTOR' : _RESPONSE,
  '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
  # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.Response)
  })
_sym_db.RegisterMessage(Response)
_sym_db.RegisterMessage(Response.ExtensionsEntry)

Info = _reflection.GeneratedProtocolMessageType('Info', (_message.Message,), {

  'ExtensionsEntry' : _reflection.GeneratedProtocolMessageType('ExtensionsEntry', (_message.Message,), {
    'DESCRIPTOR' : _INFO_EXTENSIONSENTRY,
    '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
    # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.Info.ExtensionsEntry)
    })
  ,
  'DESCRIPTOR' : _INFO,
  '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
  # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.Info)
  })
_sym_db.RegisterMessage(Info)
_sym_db.RegisterMessage(Info.ExtensionsEntry)

Contact = _reflection.GeneratedProtocolMessageType('Contact', (_message.Message,), {
  'DESCRIPTOR' : _CONTACT,
  '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
  # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.Contact)
  })
_sym_db.RegisterMessage(Contact)

License = _reflection.GeneratedProtocolMessageType('License', (_message.Message,), {
  'DESCRIPTOR' : _LICENSE,
  '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
  # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.License)
  })
_sym_db.RegisterMessage(License)

ExternalDocumentation = _reflection.GeneratedProtocolMessageType('ExternalDocumentation', (_message.Message,), {
  'DESCRIPTOR' : _EXTERNALDOCUMENTATION,
  '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
  # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.ExternalDocumentation)
  })
_sym_db.RegisterMessage(ExternalDocumentation)

Schema = _reflection.GeneratedProtocolMessageType('Schema', (_message.Message,), {
  'DESCRIPTOR' : _SCHEMA,
  '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
  # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.Schema)
  })
_sym_db.RegisterMessage(Schema)

JSONSchema = _reflection.GeneratedProtocolMessageType('JSONSchema', (_message.Message,), {
  'DESCRIPTOR' : _JSONSCHEMA,
  '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
  # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.JSONSchema)
  })
_sym_db.RegisterMessage(JSONSchema)

Tag = _reflection.GeneratedProtocolMessageType('Tag', (_message.Message,), {
  'DESCRIPTOR' : _TAG,
  '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
  # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.Tag)
  })
_sym_db.RegisterMessage(Tag)

SecurityDefinitions = _reflection.GeneratedProtocolMessageType('SecurityDefinitions', (_message.Message,), {

  'SecurityEntry' : _reflection.GeneratedProtocolMessageType('SecurityEntry', (_message.Message,), {
    'DESCRIPTOR' : _SECURITYDEFINITIONS_SECURITYENTRY,
    '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
    # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.SecurityDefinitions.SecurityEntry)
    })
  ,
  'DESCRIPTOR' : _SECURITYDEFINITIONS,
  '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
  # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.SecurityDefinitions)
  })
_sym_db.RegisterMessage(SecurityDefinitions)
_sym_db.RegisterMessage(SecurityDefinitions.SecurityEntry)

SecurityScheme = _reflection.GeneratedProtocolMessageType('SecurityScheme', (_message.Message,), {

  'ExtensionsEntry' : _reflection.GeneratedProtocolMessageType('ExtensionsEntry', (_message.Message,), {
    'DESCRIPTOR' : _SECURITYSCHEME_EXTENSIONSENTRY,
    '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
    # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.SecurityScheme.ExtensionsEntry)
    })
  ,
  'DESCRIPTOR' : _SECURITYSCHEME,
  '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
  # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.SecurityScheme)
  })
_sym_db.RegisterMessage(SecurityScheme)
_sym_db.RegisterMessage(SecurityScheme.ExtensionsEntry)

SecurityRequirement = _reflection.GeneratedProtocolMessageType('SecurityRequirement', (_message.Message,), {

  'SecurityRequirementValue' : _reflection.GeneratedProtocolMessageType('SecurityRequirementValue', (_message.Message,), {
    'DESCRIPTOR' : _SECURITYREQUIREMENT_SECURITYREQUIREMENTVALUE,
    '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
    # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.SecurityRequirement.SecurityRequirementValue)
    })
  ,

  'SecurityRequirementEntry' : _reflection.GeneratedProtocolMessageType('SecurityRequirementEntry', (_message.Message,), {
    'DESCRIPTOR' : _SECURITYREQUIREMENT_SECURITYREQUIREMENTENTRY,
    '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
    # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.SecurityRequirement.SecurityRequirementEntry)
    })
  ,
  'DESCRIPTOR' : _SECURITYREQUIREMENT,
  '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
  # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.SecurityRequirement)
  })
_sym_db.RegisterMessage(SecurityRequirement)
_sym_db.RegisterMessage(SecurityRequirement.SecurityRequirementValue)
_sym_db.RegisterMessage(SecurityRequirement.SecurityRequirementEntry)

Scopes = _reflection.GeneratedProtocolMessageType('Scopes', (_message.Message,), {

  'ScopeEntry' : _reflection.GeneratedProtocolMessageType('ScopeEntry', (_message.Message,), {
    'DESCRIPTOR' : _SCOPES_SCOPEENTRY,
    '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
    # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.Scopes.ScopeEntry)
    })
  ,
  'DESCRIPTOR' : _SCOPES,
  '__module__' : 'protoc_gen_swagger.options.openapiv2_pb2'
  # @@protoc_insertion_point(class_scope:grpc.gateway.protoc_gen_swagger.options.Scopes)
  })
_sym_db.RegisterMessage(Scopes)
_sym_db.RegisterMessage(Scopes.ScopeEntry)


DESCRIPTOR._options = None
_SWAGGER_RESPONSESENTRY._options = None
_SWAGGER_EXTENSIONSENTRY._options = None
_OPERATION_RESPONSESENTRY._options = None
_OPERATION_EXTENSIONSENTRY._options = None
_RESPONSE_EXTENSIONSENTRY._options = None
_INFO_EXTENSIONSENTRY._options = None
_SECURITYDEFINITIONS_SECURITYENTRY._options = None
_SECURITYSCHEME_EXTENSIONSENTRY._options = None
_SECURITYREQUIREMENT_SECURITYREQUIREMENTENTRY._options = None
_SCOPES_SCOPEENTRY._options = None
# @@protoc_insertion_point(module_scope)
