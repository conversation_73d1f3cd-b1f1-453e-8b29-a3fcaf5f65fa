# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: model.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from gogoproto import gogo_pb2 as gogoproto_dot_gogo__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='model.proto',
  package='jaeger.api_v2',
  syntax='proto3',
  serialized_options=_b('\n\027io.jaegertracing.api_v2Z\005model\310\342\036\001\320\342\036\001\340\342\036\001\300\343\036\001'),
  serialized_pb=_b('\n\x0bmodel.proto\x12\rjaeger.api_v2\x1a\x14gogoproto/gogo.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1egoogle/protobuf/duration.proto\"\xa0\x01\n\x08KeyValue\x12\x0b\n\x03key\x18\x01 \x01(\t\x12(\n\x06v_type\x18\x02 \x01(\x0e\x32\x18.jaeger.api_v2.ValueType\x12\r\n\x05v_str\x18\x03 \x01(\t\x12\x0e\n\x06v_bool\x18\x04 \x01(\x08\x12\x0f\n\x07v_int64\x18\x05 \x01(\x03\x12\x11\n\tv_float64\x18\x06 \x01(\x01\x12\x10\n\x08v_binary\x18\x07 \x01(\x0c:\x08\xe8\xa0\x1f\x01\xe8\xa1\x1f\x01\"m\n\x03Log\x12\x37\n\ttimestamp\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.TimestampB\x08\x90\xdf\x1f\x01\xc8\xde\x1f\x00\x12-\n\x06\x66ields\x18\x02 \x03(\x0b\x32\x17.jaeger.api_v2.KeyValueB\x04\xc8\xde\x1f\x00\"\x90\x01\n\x07SpanRef\x12,\n\x08trace_id\x18\x01 \x01(\x0c\x42\x1a\xc8\xde\x1f\x00\xda\xde\x1f\x07TraceID\xe2\xde\x1f\x07TraceID\x12)\n\x07span_id\x18\x02 \x01(\x0c\x42\x18\xc8\xde\x1f\x00\xda\xde\x1f\x06SpanID\xe2\xde\x1f\x06SpanID\x12,\n\x08ref_type\x18\x03 \x01(\x0e\x32\x1a.jaeger.api_v2.SpanRefType\"L\n\x07Process\x12\x14\n\x0cservice_name\x18\x01 \x01(\t\x12+\n\x04tags\x18\x02 \x03(\x0b\x32\x17.jaeger.api_v2.KeyValueB\x04\xc8\xde\x1f\x00\"\xeb\x03\n\x04Span\x12,\n\x08trace_id\x18\x01 \x01(\x0c\x42\x1a\xc8\xde\x1f\x00\xda\xde\x1f\x07TraceID\xe2\xde\x1f\x07TraceID\x12)\n\x07span_id\x18\x02 \x01(\x0c\x42\x18\xc8\xde\x1f\x00\xda\xde\x1f\x06SpanID\xe2\xde\x1f\x06SpanID\x12\x16\n\x0eoperation_name\x18\x03 \x01(\t\x12\x30\n\nreferences\x18\x04 \x03(\x0b\x32\x16.jaeger.api_v2.SpanRefB\x04\xc8\xde\x1f\x00\x12\x1c\n\x05\x66lags\x18\x05 \x01(\rB\r\xc8\xde\x1f\x00\xda\xde\x1f\x05\x46lags\x12\x38\n\nstart_time\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.TimestampB\x08\x90\xdf\x1f\x01\xc8\xde\x1f\x00\x12\x35\n\x08\x64uration\x18\x07 \x01(\x0b\x32\x19.google.protobuf.DurationB\x08\x98\xdf\x1f\x01\xc8\xde\x1f\x00\x12+\n\x04tags\x18\x08 \x03(\x0b\x32\x17.jaeger.api_v2.KeyValueB\x04\xc8\xde\x1f\x00\x12&\n\x04logs\x18\t \x03(\x0b\x32\x12.jaeger.api_v2.LogB\x04\xc8\xde\x1f\x00\x12\'\n\x07process\x18\n \x01(\x0b\x32\x16.jaeger.api_v2.Process\x12!\n\nprocess_id\x18\x0b \x01(\tB\r\xe2\xde\x1f\tProcessID\x12\x10\n\x08warnings\x18\x0c \x03(\t\"\xe1\x01\n\x05Trace\x12\"\n\x05spans\x18\x01 \x03(\x0b\x32\x13.jaeger.api_v2.Span\x12>\n\x0bprocess_map\x18\x02 \x03(\x0b\x32#.jaeger.api_v2.Trace.ProcessMappingB\x04\xc8\xde\x1f\x00\x12\x10\n\x08warnings\x18\x03 \x03(\t\x1a\x62\n\x0eProcessMapping\x12!\n\nprocess_id\x18\x01 \x01(\tB\r\xe2\xde\x1f\tProcessID\x12-\n\x07process\x18\x02 \x01(\x0b\x32\x16.jaeger.api_v2.ProcessB\x04\xc8\xde\x1f\x00\"Z\n\x05\x42\x61tch\x12\"\n\x05spans\x18\x01 \x03(\x0b\x32\x13.jaeger.api_v2.Span\x12-\n\x07process\x18\x02 \x01(\x0b\x32\x16.jaeger.api_v2.ProcessB\x04\xc8\xde\x1f\x01\"S\n\x0e\x44\x65pendencyLink\x12\x0e\n\x06parent\x18\x01 \x01(\t\x12\r\n\x05\x63hild\x18\x02 \x01(\t\x12\x12\n\ncall_count\x18\x03 \x01(\x04\x12\x0e\n\x06source\x18\x04 \x01(\t*E\n\tValueType\x12\n\n\x06STRING\x10\x00\x12\x08\n\x04\x42OOL\x10\x01\x12\t\n\x05INT64\x10\x02\x12\x0b\n\x07\x46LOAT64\x10\x03\x12\n\n\x06\x42INARY\x10\x04*-\n\x0bSpanRefType\x12\x0c\n\x08\x43HILD_OF\x10\x00\x12\x10\n\x0c\x46OLLOWS_FROM\x10\x01\x42\x30\n\x17io.jaegertracing.api_v2Z\x05model\xc8\xe2\x1e\x01\xd0\xe2\x1e\x01\xe0\xe2\x1e\x01\xc0\xe3\x1e\x01\x62\x06proto3')
  ,
  dependencies=[gogoproto_dot_gogo__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,google_dot_protobuf_dot_duration__pb2.DESCRIPTOR,])

_VALUETYPE = _descriptor.EnumDescriptor(
  name='ValueType',
  full_name='jaeger.api_v2.ValueType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='STRING', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BOOL', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INT64', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FLOAT64', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BINARY', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1515,
  serialized_end=1584,
)
_sym_db.RegisterEnumDescriptor(_VALUETYPE)

ValueType = enum_type_wrapper.EnumTypeWrapper(_VALUETYPE)
_SPANREFTYPE = _descriptor.EnumDescriptor(
  name='SpanRefType',
  full_name='jaeger.api_v2.SpanRefType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CHILD_OF', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FOLLOWS_FROM', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1586,
  serialized_end=1631,
)
_sym_db.RegisterEnumDescriptor(_SPANREFTYPE)

SpanRefType = enum_type_wrapper.EnumTypeWrapper(_SPANREFTYPE)
STRING = 0
BOOL = 1
INT64 = 2
FLOAT64 = 3
BINARY = 4
CHILD_OF = 0
FOLLOWS_FROM = 1



_KEYVALUE = _descriptor.Descriptor(
  name='KeyValue',
  full_name='jaeger.api_v2.KeyValue',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='jaeger.api_v2.KeyValue.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='v_type', full_name='jaeger.api_v2.KeyValue.v_type', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='v_str', full_name='jaeger.api_v2.KeyValue.v_str', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='v_bool', full_name='jaeger.api_v2.KeyValue.v_bool', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='v_int64', full_name='jaeger.api_v2.KeyValue.v_int64', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='v_float64', full_name='jaeger.api_v2.KeyValue.v_float64', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='v_binary', full_name='jaeger.api_v2.KeyValue.v_binary', index=6,
      number=7, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('\350\240\037\001\350\241\037\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=118,
  serialized_end=278,
)


_LOG = _descriptor.Descriptor(
  name='Log',
  full_name='jaeger.api_v2.Log',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='jaeger.api_v2.Log.timestamp', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\220\337\037\001\310\336\037\000'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='jaeger.api_v2.Log.fields', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\310\336\037\000'), file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=280,
  serialized_end=389,
)


_SPANREF = _descriptor.Descriptor(
  name='SpanRef',
  full_name='jaeger.api_v2.SpanRef',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='trace_id', full_name='jaeger.api_v2.SpanRef.trace_id', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\310\336\037\000\332\336\037\007TraceID\342\336\037\007TraceID'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='span_id', full_name='jaeger.api_v2.SpanRef.span_id', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\310\336\037\000\332\336\037\006SpanID\342\336\037\006SpanID'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ref_type', full_name='jaeger.api_v2.SpanRef.ref_type', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=392,
  serialized_end=536,
)


_PROCESS = _descriptor.Descriptor(
  name='Process',
  full_name='jaeger.api_v2.Process',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='service_name', full_name='jaeger.api_v2.Process.service_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tags', full_name='jaeger.api_v2.Process.tags', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\310\336\037\000'), file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=538,
  serialized_end=614,
)


_SPAN = _descriptor.Descriptor(
  name='Span',
  full_name='jaeger.api_v2.Span',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='trace_id', full_name='jaeger.api_v2.Span.trace_id', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\310\336\037\000\332\336\037\007TraceID\342\336\037\007TraceID'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='span_id', full_name='jaeger.api_v2.Span.span_id', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\310\336\037\000\332\336\037\006SpanID\342\336\037\006SpanID'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operation_name', full_name='jaeger.api_v2.Span.operation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='references', full_name='jaeger.api_v2.Span.references', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\310\336\037\000'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='flags', full_name='jaeger.api_v2.Span.flags', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\310\336\037\000\332\336\037\005Flags'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='jaeger.api_v2.Span.start_time', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\220\337\037\001\310\336\037\000'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='duration', full_name='jaeger.api_v2.Span.duration', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\230\337\037\001\310\336\037\000'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tags', full_name='jaeger.api_v2.Span.tags', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\310\336\037\000'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logs', full_name='jaeger.api_v2.Span.logs', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\310\336\037\000'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process', full_name='jaeger.api_v2.Span.process', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_id', full_name='jaeger.api_v2.Span.process_id', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\342\336\037\tProcessID'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='warnings', full_name='jaeger.api_v2.Span.warnings', index=11,
      number=12, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=617,
  serialized_end=1108,
)


_TRACE_PROCESSMAPPING = _descriptor.Descriptor(
  name='ProcessMapping',
  full_name='jaeger.api_v2.Trace.ProcessMapping',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='process_id', full_name='jaeger.api_v2.Trace.ProcessMapping.process_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\342\336\037\tProcessID'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process', full_name='jaeger.api_v2.Trace.ProcessMapping.process', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\310\336\037\000'), file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1238,
  serialized_end=1336,
)

_TRACE = _descriptor.Descriptor(
  name='Trace',
  full_name='jaeger.api_v2.Trace',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='spans', full_name='jaeger.api_v2.Trace.spans', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_map', full_name='jaeger.api_v2.Trace.process_map', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\310\336\037\000'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='warnings', full_name='jaeger.api_v2.Trace.warnings', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_TRACE_PROCESSMAPPING, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1111,
  serialized_end=1336,
)


_BATCH = _descriptor.Descriptor(
  name='Batch',
  full_name='jaeger.api_v2.Batch',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='spans', full_name='jaeger.api_v2.Batch.spans', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process', full_name='jaeger.api_v2.Batch.process', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\310\336\037\001'), file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1338,
  serialized_end=1428,
)


_DEPENDENCYLINK = _descriptor.Descriptor(
  name='DependencyLink',
  full_name='jaeger.api_v2.DependencyLink',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='parent', full_name='jaeger.api_v2.DependencyLink.parent', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='child', full_name='jaeger.api_v2.DependencyLink.child', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='call_count', full_name='jaeger.api_v2.DependencyLink.call_count', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source', full_name='jaeger.api_v2.DependencyLink.source', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1430,
  serialized_end=1513,
)

_KEYVALUE.fields_by_name['v_type'].enum_type = _VALUETYPE
_LOG.fields_by_name['timestamp'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LOG.fields_by_name['fields'].message_type = _KEYVALUE
_SPANREF.fields_by_name['ref_type'].enum_type = _SPANREFTYPE
_PROCESS.fields_by_name['tags'].message_type = _KEYVALUE
_SPAN.fields_by_name['references'].message_type = _SPANREF
_SPAN.fields_by_name['start_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SPAN.fields_by_name['duration'].message_type = google_dot_protobuf_dot_duration__pb2._DURATION
_SPAN.fields_by_name['tags'].message_type = _KEYVALUE
_SPAN.fields_by_name['logs'].message_type = _LOG
_SPAN.fields_by_name['process'].message_type = _PROCESS
_TRACE_PROCESSMAPPING.fields_by_name['process'].message_type = _PROCESS
_TRACE_PROCESSMAPPING.containing_type = _TRACE
_TRACE.fields_by_name['spans'].message_type = _SPAN
_TRACE.fields_by_name['process_map'].message_type = _TRACE_PROCESSMAPPING
_BATCH.fields_by_name['spans'].message_type = _SPAN
_BATCH.fields_by_name['process'].message_type = _PROCESS
DESCRIPTOR.message_types_by_name['KeyValue'] = _KEYVALUE
DESCRIPTOR.message_types_by_name['Log'] = _LOG
DESCRIPTOR.message_types_by_name['SpanRef'] = _SPANREF
DESCRIPTOR.message_types_by_name['Process'] = _PROCESS
DESCRIPTOR.message_types_by_name['Span'] = _SPAN
DESCRIPTOR.message_types_by_name['Trace'] = _TRACE
DESCRIPTOR.message_types_by_name['Batch'] = _BATCH
DESCRIPTOR.message_types_by_name['DependencyLink'] = _DEPENDENCYLINK
DESCRIPTOR.enum_types_by_name['ValueType'] = _VALUETYPE
DESCRIPTOR.enum_types_by_name['SpanRefType'] = _SPANREFTYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

KeyValue = _reflection.GeneratedProtocolMessageType('KeyValue', (_message.Message,), {
  'DESCRIPTOR' : _KEYVALUE,
  '__module__' : 'model_pb2'
  # @@protoc_insertion_point(class_scope:jaeger.api_v2.KeyValue)
  })
_sym_db.RegisterMessage(KeyValue)

Log = _reflection.GeneratedProtocolMessageType('Log', (_message.Message,), {
  'DESCRIPTOR' : _LOG,
  '__module__' : 'model_pb2'
  # @@protoc_insertion_point(class_scope:jaeger.api_v2.Log)
  })
_sym_db.RegisterMessage(Log)

SpanRef = _reflection.GeneratedProtocolMessageType('SpanRef', (_message.Message,), {
  'DESCRIPTOR' : _SPANREF,
  '__module__' : 'model_pb2'
  # @@protoc_insertion_point(class_scope:jaeger.api_v2.SpanRef)
  })
_sym_db.RegisterMessage(SpanRef)

Process = _reflection.GeneratedProtocolMessageType('Process', (_message.Message,), {
  'DESCRIPTOR' : _PROCESS,
  '__module__' : 'model_pb2'
  # @@protoc_insertion_point(class_scope:jaeger.api_v2.Process)
  })
_sym_db.RegisterMessage(Process)

Span = _reflection.GeneratedProtocolMessageType('Span', (_message.Message,), {
  'DESCRIPTOR' : _SPAN,
  '__module__' : 'model_pb2'
  # @@protoc_insertion_point(class_scope:jaeger.api_v2.Span)
  })
_sym_db.RegisterMessage(Span)

Trace = _reflection.GeneratedProtocolMessageType('Trace', (_message.Message,), {

  'ProcessMapping' : _reflection.GeneratedProtocolMessageType('ProcessMapping', (_message.Message,), {
    'DESCRIPTOR' : _TRACE_PROCESSMAPPING,
    '__module__' : 'model_pb2'
    # @@protoc_insertion_point(class_scope:jaeger.api_v2.Trace.ProcessMapping)
    })
  ,
  'DESCRIPTOR' : _TRACE,
  '__module__' : 'model_pb2'
  # @@protoc_insertion_point(class_scope:jaeger.api_v2.Trace)
  })
_sym_db.RegisterMessage(Trace)
_sym_db.RegisterMessage(Trace.ProcessMapping)

Batch = _reflection.GeneratedProtocolMessageType('Batch', (_message.Message,), {
  'DESCRIPTOR' : _BATCH,
  '__module__' : 'model_pb2'
  # @@protoc_insertion_point(class_scope:jaeger.api_v2.Batch)
  })
_sym_db.RegisterMessage(Batch)

DependencyLink = _reflection.GeneratedProtocolMessageType('DependencyLink', (_message.Message,), {
  'DESCRIPTOR' : _DEPENDENCYLINK,
  '__module__' : 'model_pb2'
  # @@protoc_insertion_point(class_scope:jaeger.api_v2.DependencyLink)
  })
_sym_db.RegisterMessage(DependencyLink)


DESCRIPTOR._options = None
_KEYVALUE._options = None
_LOG.fields_by_name['timestamp']._options = None
_LOG.fields_by_name['fields']._options = None
_SPANREF.fields_by_name['trace_id']._options = None
_SPANREF.fields_by_name['span_id']._options = None
_PROCESS.fields_by_name['tags']._options = None
_SPAN.fields_by_name['trace_id']._options = None
_SPAN.fields_by_name['span_id']._options = None
_SPAN.fields_by_name['references']._options = None
_SPAN.fields_by_name['flags']._options = None
_SPAN.fields_by_name['start_time']._options = None
_SPAN.fields_by_name['duration']._options = None
_SPAN.fields_by_name['tags']._options = None
_SPAN.fields_by_name['logs']._options = None
_SPAN.fields_by_name['process_id']._options = None
_TRACE_PROCESSMAPPING.fields_by_name['process_id']._options = None
_TRACE_PROCESSMAPPING.fields_by_name['process']._options = None
_TRACE.fields_by_name['process_map']._options = None
_BATCH.fields_by_name['process']._options = None
# @@protoc_insertion_point(module_scope)
