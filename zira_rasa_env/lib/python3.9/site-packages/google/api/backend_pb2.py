# -*- coding: utf-8 -*-

# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/backend.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x18google/api/backend.proto\x12\ngoogle.api"1\n\x07\x42\x61\x63kend\x12&\n\x05rules\x18\x01 \x03(\x0b\x32\x17.google.api.BackendRule"\xf2\x02\n\x0b\x42\x61\x63kendRule\x12\x10\n\x08selector\x18\x01 \x01(\t\x12\x0f\n\x07\x61\x64\x64ress\x18\x02 \x01(\t\x12\x10\n\x08\x64\x65\x61\x64line\x18\x03 \x01(\x01\x12\x14\n\x0cmin_deadline\x18\x04 \x01(\x01\x12\x1a\n\x12operation_deadline\x18\x05 \x01(\x01\x12\x41\n\x10path_translation\x18\x06 \x01(\x0e\x32\'.google.api.BackendRule.PathTranslation\x12\x16\n\x0cjwt_audience\x18\x07 \x01(\tH\x00\x12\x16\n\x0c\x64isable_auth\x18\x08 \x01(\x08H\x00\x12\x10\n\x08protocol\x18\t \x01(\t"e\n\x0fPathTranslation\x12 \n\x1cPATH_TRANSLATION_UNSPECIFIED\x10\x00\x12\x14\n\x10\x43ONSTANT_ADDRESS\x10\x01\x12\x1a\n\x16\x41PPEND_PATH_TO_ADDRESS\x10\x02\x42\x10\n\x0e\x61uthenticationBn\n\x0e\x63om.google.apiB\x0c\x42\x61\x63kendProtoP\x01ZEgoogle.golang.org/genproto/googleapis/api/serviceconfig;serviceconfig\xa2\x02\x04GAPIb\x06proto3'
)


_BACKEND = DESCRIPTOR.message_types_by_name["Backend"]
_BACKENDRULE = DESCRIPTOR.message_types_by_name["BackendRule"]
_BACKENDRULE_PATHTRANSLATION = _BACKENDRULE.enum_types_by_name["PathTranslation"]
Backend = _reflection.GeneratedProtocolMessageType(
    "Backend",
    (_message.Message,),
    {
        "DESCRIPTOR": _BACKEND,
        "__module__": "google.api.backend_pb2"
        # @@protoc_insertion_point(class_scope:google.api.Backend)
    },
)
_sym_db.RegisterMessage(Backend)

BackendRule = _reflection.GeneratedProtocolMessageType(
    "BackendRule",
    (_message.Message,),
    {
        "DESCRIPTOR": _BACKENDRULE,
        "__module__": "google.api.backend_pb2"
        # @@protoc_insertion_point(class_scope:google.api.BackendRule)
    },
)
_sym_db.RegisterMessage(BackendRule)

if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\016com.google.apiB\014BackendProtoP\001ZEgoogle.golang.org/genproto/googleapis/api/serviceconfig;serviceconfig\242\002\004GAPI"
    _BACKEND._serialized_start = 40
    _BACKEND._serialized_end = 89
    _BACKENDRULE._serialized_start = 92
    _BACKENDRULE._serialized_end = 462
    _BACKENDRULE_PATHTRANSLATION._serialized_start = 343
    _BACKENDRULE_PATHTRANSLATION._serialized_end = 444
# @@protoc_insertion_point(module_scope)
