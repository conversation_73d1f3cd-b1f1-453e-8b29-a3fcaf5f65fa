Metadata-Version: 2.1
Name: opentelemetry-exporter-jaeger-proto-grpc
Version: 1.16.0
Summary: <PERSON><PERSON>ger Protobuf Exporter for OpenTelemetry
Project-URL: Homepage, https://github.com/open-telemetry/opentelemetry-python/tree/main/exporter/opentelemetry-exporter-jaeger-proto-grpc
Author-email: OpenTelemetry Authors <<EMAIL>>
License-Expression: Apache-2.0
License-File: LICENSE
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Typing :: Typed
Requires-Python: >=3.7
Requires-Dist: googleapis-common-protos<1.56.3,~=1.52
Requires-Dist: grpcio<2.0.0,>=1.0.0
Requires-Dist: opentelemetry-api~=1.3
Requires-Dist: opentelemetry-sdk~=1.11
Provides-Extra: test
Description-Content-Type: text/x-rst

OpenTelemetry Jaeger Protobuf Exporter
======================================

.. warning::
    Since v1.35, the Jaeger supports OTLP natively. Please use the OTLP exporter instead.
    Support for this exporter will end July 2023.

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-exporter-jaeger-proto-grpc.svg
   :target: https://pypi.org/project/opentelemetry-exporter-jaeger-proto-grpc/

This library allows to export tracing data to `Jaeger <https://www.jaegertracing.io/>`_.

Installation
------------

::

    pip install opentelemetry-exporter-jaeger-proto-grpc


.. _Jaeger: https://www.jaegertracing.io/
.. _OpenTelemetry: https://github.com/open-telemetry/opentelemetry-python/

Configuration
-------------

OpenTelemetry Jaeger Exporter can be configured by setting `JaegerExporter parameters
<https://github.com/open-telemetry/opentelemetry-python/blob/main/exporter/opentelemetry-exporter-jaeger-proto-grpc
/src/opentelemetry/exporter/jaeger/proto/__init__.py#L88>`_ or by setting
`environment variables <https://github.com/open-telemetry/opentelemetry-specification/blob/main/
specification/sdk-environment-variables.md#jaeger-exporter>`_

References
----------

* `OpenTelemetry Jaeger Exporter <https://opentelemetry-python.readthedocs.io/en/latest/exporter/jaeger/jaeger.html>`_
* `Jaeger <https://www.jaegertracing.io/>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
