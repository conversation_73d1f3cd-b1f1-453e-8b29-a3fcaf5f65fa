from pathlib import Path
from typing import Optional, Text, Union

from ruamel.yaml.error import (
    MarkedYAM<PERSON>rror,
    MarkedYAMLWarning,
    MarkedYAMLFutureWarning,
)


class FileNotFoundException(FileNotFoundError):
    """Raised when a file, expected to exist, doesn't exist."""


class FileIOException(Exception):
    """Raised if there is an error while doing file IO."""


class YamlSyntaxException(Exception):
    """Raised when a YAML file can not be parsed properly due to a syntax error."""

    def __init__(
        self,
        filename: Optional[Union[Text, Path]] = None,
        underlying_yaml_exception: Optional[Exception] = None,
    ) -> None:
        """Represents the exception constructor."""
        self.filename = filename

        self.underlying_yaml_exception = underlying_yaml_exception

    def __str__(self) -> Text:
        if self.filename:
            exception_text = f"Failed to read '{self.filename}'."
        else:
            exception_text = "Failed to read YAML."

        if self.underlying_yaml_exception:
            if isinstance(
                self.underlying_yaml_exception,
                (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MarkedY<PERSON>LFutureWarning),
            ):
                self.underlying_yaml_exception.note = None
            if isinstance(
                self.underlying_yaml_exception,
                (MarkedYAMLWarning, MarkedYAMLFutureWarning),
            ):
                self.underlying_yaml_exception.warn = None
            exception_text += f" {self.underlying_yaml_exception}"

        if self.filename:
            exception_text = exception_text.replace(
                'in "<unicode string>"', f'in "{self.filename}"'
            )

        exception_text += (
            "\n\nYou can use https://yamlchecker.com/ to validate the "
            "YAML syntax of your file."
        )
        return exception_text
