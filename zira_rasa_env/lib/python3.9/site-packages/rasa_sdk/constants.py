DEFAULT_SERVER_PORT = 5055
DEFAULT_SANIC_WORKERS = 1
DEFAULT_KEEP_ALIVE_TIMEOUT = 120  # in seconds
ENV_SANIC_WORKERS = "ACTION_SERVER_SANIC_WORKERS"
DEFAULT_LOG_LEVEL_LIBRARIES = "ERROR"
ENV_LOG_LEVEL_LIBRARIES = "LOG_LEVEL_LIBRARIES"
APPLICATION_ROOT_LOGGER_NAME = "rasa_sdk"
DEFAULT_ENCODING = "utf-8"
YAML_VERSION = (1, 2)
PYTHON_LOGGING_SCHEMA_DOCS = (
    "https://docs.python.org/3/library/logging.config.html#dictionary-schema-details"
)
DEFAULT_ENDPOINTS_PATH = "endpoints.yml"
NO_GRACE_PERIOD = 0
