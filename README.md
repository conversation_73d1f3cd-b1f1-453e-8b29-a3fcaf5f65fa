# ZIRA - AI Strategic Assistant

**ZIRA** (Strategic Intelligence & Revenue Assessment) is an advanced AI chatbot developed for Techpulse Consulting. It conducts structured business diagnostics and provides preliminary project assessments with sophisticated business intelligence capabilities.

## 🚀 Features

- **Strategic Business Assessment**: Comprehensive project evaluation with Value Impact Index (VII) calculation
- **5 Partnership Models**: Crisis-to-Success, Performance, Equity, Premium, and Monthly Flow models
- **Swiss Market Focus**: CHF currency and Swiss business formatting
- **Advanced Calculations**: Sophisticated mathematical formulas for project valuation
- **Professional Communication**: Encouraging and professional interaction style
- **Web Integration Ready**: Seamless integration with Techpulse Consulting website

## 📋 Prerequisites

- Python 3.9+ (tested with Python 3.9.6)
- Virtual environment (recommended)
- Rasa SDK 3.12.1+

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   cd /path/to/your/project
   ```

2. **Create and activate virtual environment**:
   ```bash
   python3 -m venv zira_rasa_env
   source zira_rasa_env/bin/activate  # On Windows: zira_rasa_env\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

## 🏃‍♂️ Running ZIRA

### Method 1: Using Rasa SDK Only (Recommended for Development)

1. **Start the action server**:
   ```bash
   source zira_rasa_env/bin/activate
   rasa run actions --port 5055
   ```

2. **In a new terminal, start the Rasa server**:
   ```bash
   source zira_rasa_env/bin/activate
   rasa run --enable-api --cors "*" --port 5005
   ```

3. **Open the web interface**:
   - Open `web/index.html` in your browser
   - Or serve it with a simple HTTP server:
   ```bash
   cd web
   python -m http.server 8000
   ```
   - Then visit `http://localhost:8000`

### Method 2: Training and Running Full Rasa Model

If you have Rasa Open Source installed:

1. **Train the model**:
   ```bash
   rasa train
   ```

2. **Run the trained model**:
   ```bash
   rasa run --enable-api --cors "*"
   ```

## 🧪 Testing ZIRA

### Quick Test Conversation

1. Start with: "Hello"
2. Say: "I want to start an assessment"
3. Follow the guided questions:
   - Company name: "TechStart"
   - Industry: "technology"
   - Company size: "startup"
   - Project type: "software development"
   - Project scope: "mobile app"
   - Budget range: "50K-200K CHF"
   - Timeline: "6 months"
   - Team size: "5"
   - Challenges: "need digital presence"

4. ZIRA will calculate and provide:
   - Project Value (in CHF)
   - Estimated Timeline (in months)
   - Recommended Partnership Model

### Test Partnership Models

Ask: "What partnership models do you offer?" to see all 5 models explained.

## 🏗️ Architecture

### Core Components

1. **Domain Configuration** (`domain.yml`): Defines intents, entities, slots, and responses
2. **NLU Training Data** (`data/nlu.yml`): Training examples for intent recognition
3. **Conversation Stories** (`data/stories.yml`): Example conversation flows
4. **Rules** (`data/rules.yml`): Deterministic conversation rules
5. **Custom Actions** (`actions/actions.py`): Business logic and calculations
6. **Web Interface** (`web/index.html`): User-friendly chat interface

### Business Logic Features

- **Value Impact Index (VII)**: Multi-factor calculation considering industry, company size, project type, and complexity
- **Dynamic Pricing**: Sophisticated algorithms for project valuation
- **Partnership Recommendation**: AI-driven model selection based on client profile
- **Swiss Formatting**: CHF currency and Swiss business standards

## 🌐 Website Integration

### For Techpulse Consulting Website

1. **Upload the web interface** to your website
2. **Ensure CORS is enabled** on the Rasa server
3. **Update the RASA_SERVER_URL** in the HTML file to point to your production server
4. **Embed the chat widget** in your action-hub page

### Integration Code Example

```html
<!-- Add this to your website -->
<iframe src="/path/to/zira/web/index.html" 
        width="100%" 
        height="600px" 
        frameborder="0">
</iframe>
```

## 🔧 Configuration

### Customizing Responses

Edit `domain.yml` to modify ZIRA's responses and personality.

### Adjusting Business Logic

Modify `actions/actions.py` to:
- Update calculation formulas
- Add new partnership models
- Adjust industry multipliers
- Customize assessment criteria

### Adding New Intents

1. Add examples to `data/nlu.yml`
2. Create stories in `data/stories.yml`
3. Add responses to `domain.yml`
4. Implement custom actions if needed

## 📊 Partnership Models

1. **Crisis-to-Success**: For urgent, critical situations with rapid response
2. **Performance**: Results-driven with KPI-based payments
3. **Equity**: Partnership through equity exchange for startups
4. **Premium**: Full-service, white-glove experience for high-value projects
5. **Monthly Flow**: Predictable monthly payments with flexible scope

## 🚀 Deployment

### Production Deployment

1. **Set up a production server** (Linux recommended)
2. **Install dependencies** in production environment
3. **Configure reverse proxy** (nginx recommended)
4. **Set up SSL certificates** for HTTPS
5. **Configure monitoring** and logging
6. **Set up backup procedures** for conversation data

### Environment Variables

Create a `.env` file for production:
```
RASA_SERVER_URL=https://your-domain.com/rasa
ACTION_SERVER_URL=https://your-domain.com/actions
```

## 🔒 Security Considerations

- Enable HTTPS in production
- Implement rate limiting
- Set up proper CORS policies
- Monitor for unusual activity
- Regular security updates

## 📈 Analytics and Monitoring

- Track conversation completion rates
- Monitor assessment accuracy
- Analyze partnership model recommendations
- User satisfaction metrics

## 🆘 Troubleshooting

### Common Issues

1. **Action server not responding**: Check if port 5055 is available
2. **CORS errors**: Ensure `--cors "*"` flag is used
3. **Model not found**: Run `rasa train` first
4. **Dependencies issues**: Check Python version compatibility

### Debug Mode

Run with debug logging:
```bash
rasa run --debug
```

## 📞 Support

For technical support or customization requests, contact Techpulse Consulting.

## 📄 License

This project is proprietary to Techpulse Consulting. All rights reserved.

---

**ZIRA** - Transforming business intelligence through AI-powered strategic assessment.
